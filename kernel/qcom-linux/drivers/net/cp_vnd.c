/* SPDX-License-Identifier: GPL-2.0-only */
/*
 * Copyright (c) 2023 Nordix Foundation
 *
 */

#include <linux/kernel.h>
#include <linux/types.h>
#include <linux/module.h>
#include <linux/init.h>
#include <linux/list.h>
#include <linux/errno.h>
#include <linux/notifier.h>
#include <linux/netdevice.h>
#include <linux/etherdevice.h>
#include <linux/ethtool.h>
#include <linux/if_arp.h>
#include <linux/if_vlan.h>
#include <linux/inetdevice.h>
#include <linux/if_link.h>
#include <linux/hash.h>
#include <linux/workqueue.h>
#include <net/rtnetlink.h>
#include <net/xfrm.h>
#include <linux/netpoll.h>

#include <uapi/linux/cpvnd.h>

#define DEBUG 0

/* struct cpvnd_filter_list
 *
 *	cpvnd_filter: Filter data from userspace
 *  rcu: for kfree_rcu and friends
 *  next: pointer to the next element in the filter list
 */
struct cpvnd_filter_list {
	struct cpvnd_filter cpvnd_filter;
	struct rcu_head rcu;
	struct list_head next;
};

/* struct cpvnd
 *	This driver's instance data
 *
 *	filters: all the filters associated with this device.
 *	vnd: This instance's netdevice
 *	real_dev:  Egress device we're filtering.
 */
struct cpvnd {
	struct list_head filters;
	struct net_device *vnd;
	struct net_device *real_dev;
};

#if DEBUG
static void cpvnd_print_filter(struct cpvnd_filter *cpvnd_filter)
{
	pr_debug("----------cpvnd_filter START----------\n");
	pr_debug("    ifname: %s\n", cpvnd_filter->subnet.ifname);
	pr_debug("    filter_key: %d\n", cpvnd_filter->subnet.filter_key);
	pr_debug("    filter_key_offset: %d\n", cpvnd_filter->subnet.filter_key_offset);
	pr_debug("    type: %d\n", (int)cpvnd_filter->type);
	pr_debug("----------cpvnd_filter END----------\n");
}
#endif

static rx_handler_result_t cpvnd_handle_frame(struct sk_buff **pskb);

/* We only care about the UNREGISTER event, and only for real_dev */
static int cpvnd_device_event(struct notifier_block *unused,
			      unsigned long event, void *ptr)
{
	struct net_device *dev;
	struct cpvnd *cpvnd;

	if (!ptr)
		return -EINVAL;

	dev = netdev_notifier_info_to_dev(ptr);
	if (!dev)
		return -EINVAL;

	netdev_dbg(dev, "In %s, event=%lu for dev=%s\n", __func__, event, dev->name);
	switch (event) {
	case NETDEV_UNREGISTER:
		/* We only care about the case where real_dev is getting unregistered,
		 * and we want to unregister ourselves in as well in this case */
		if (cpvnd_handle_frame != rtnl_dereference(dev->rx_handler)) {
			netdev_dbg(dev, "In %s, event=%lu don't care because rxhander isn't ours\n", __func__, event);
			break;
		}

		if (dev->reg_state != NETREG_UNREGISTERING) {
			netdev_dbg(dev, "In %s, event=%lu don't care because event isn't deregistering\n", __func__, event);
			break;
		}

		/* Called within the RTNL lock, so we want to rntl_dereference instead
		 * of just rcu_dereference. */
		cpvnd = rtnl_dereference(dev->rx_handler_data);

		rtnl_delete_link(cpvnd->vnd);
		break;
	};
	return NOTIFY_DONE;
}

static struct notifier_block cpvnd_notifier_block __read_mostly = {
	.notifier_call	= cpvnd_device_event,
};

/* Forward skbs received on real_dev (the egress device we're filtering) to
 * the upper layers */
static rx_handler_result_t cpvnd_handle_frame(struct sk_buff **pskb)
{
	struct cpvnd *cpvnd;
	struct sk_buff *skb = *pskb;

	cpvnd = rcu_dereference(skb->dev->rx_handler_data);

	if (!cpvnd)
		return RX_HANDLER_PASS;

	netdev_dbg(skb->dev, "In %s, with skb->dev=%s and real_dev=%s,forwarding to vdev=%s\n",
		   __func__, skb->dev->name, cpvnd->real_dev->name, cpvnd->vnd->name);

	skb->dev = cpvnd->vnd;

	return RX_HANDLER_ANOTHER;
}

/* Return >0 if we should drop this packet.
 * forward the packet
 */
static int drop_packet(struct sk_buff *skb, struct cpvnd *cpvnd)
{
	u8 filter_key;
	struct in_device *ip_dev;
	struct cpvnd_filter_list *filter, *temp;
	struct in_ifaddr *ifa;
	struct net_device *source_dev = NULL;
	struct iphdr *hdr = ip_hdr(skb);

	if (!cpvnd) {
		netdev_dbg(skb->dev, "In %s, filters is NULL, allowing packet\n", __func__);
		return 0; /* Allow it */
	}

	if (!hdr)
		return 0; /* Allow invalid headers to pass through */

	/* Allow  IPv6 */
	if (hdr->version != 4)
		return 0;

	/* Don't drop src addresses of 0.0.0.0 */
	if (hdr->saddr == 0)
		return 0;

	filter = NULL;

	list_for_each_entry_rcu(temp, &cpvnd->filters, next) {
		netdev_dbg(skb->dev, "In %s at %s:%d\n", __func__, __FILE__, __LINE__);
#if DEBUG
		cpvnd_print_filter(&temp->cpvnd_filter);
#endif
		if (skb->len < (uint32_t)temp->cpvnd_filter.subnet.filter_key_offset) {
			netdev_err(skb->dev, "Skipping filter check since skb->len (%u) < filter key offset (%d)\n", skb->len, temp->cpvnd_filter.subnet.filter_key_offset);
			continue;
		}
		filter_key = *((uint8_t const *)(skb->data + (uint8_t)temp->cpvnd_filter.subnet.filter_key_offset));
		netdev_dbg(skb->dev, "SKB's filter_key=0x%x, filter's filter_key=0x%x\n", filter_key, temp->cpvnd_filter.subnet.filter_key);
		if (temp->cpvnd_filter.subnet.filter_key == filter_key) {
			filter = temp;
			netdev_dbg(skb->dev, "Found filter mux id 0x%x == skb mux id (0x%x)\n", temp->cpvnd_filter.subnet.filter_key, filter_key);
			break;
		}
	}

	if (!filter) {
		netdev_dbg(skb->dev, "In %s at %s:%d\n", __func__, __FILE__, __LINE__);
		return 0; /* Allow it */
	}

	netdev_dbg(skb->dev, "In %s, lookup source_dev using ifname=%s\n",
		   __func__, filter->cpvnd_filter.subnet.ifname);

	source_dev  = dev_get_by_name(dev_net(skb->dev),
				      filter->cpvnd_filter.subnet.ifname);
	if (!source_dev) {
		netdev_info(skb->dev, "In %s,failed to get source dev, allowing packet\n", __func__);
		return 0; /* Allow it */
	}

	ip_dev = in_dev_get(source_dev);
	if (!ip_dev) {
		netdev_err(skb->dev, "Failed to get ip_dev in %s\n", __func__);
		dev_put(source_dev);
		return 0;
	}

	/* We have found a filter to match against, assume we'll now drop the
	* packet unless source_ip matches */
	in_dev_for_each_ifa_rcu(ifa, ip_dev) {
		if (ifa && inet_ifa_match(hdr->saddr, ifa)) {
			in_dev_put(ip_dev);
			dev_put(source_dev);
			netdev_dbg(skb->dev, "In %s, allowing packet with dst_ip=%pI4n, src_ip=%pI4n\n", __func__,  &hdr->daddr, &hdr->saddr);
			return 0; /* Allow it */
		}
	}
	in_dev_put(ip_dev);
	dev_put(source_dev);
	netdev_dbg(skb->dev, "In %s, dropping packet with dst_ip=%pI4n, src_ip=%pI4n\n", __func__,  &hdr->daddr, &hdr->saddr);
	return 1;  /* Drop it if we have found a filter, but said filter didn't have a matching source IP */
}

static netdev_tx_t cpvnd_start_xmit(struct sk_buff *skb,
				    struct net_device *dev)
{
	struct cpvnd *cpvnd = netdev_priv(dev);

	netdev_dbg(dev, "In %s, dev=%s,skb->dev=%s forwarding to %s\n",
		   __func__, dev->name, skb->dev->name, cpvnd->real_dev->name);

	if (!cpvnd) {
		pr_err("in %s with NULL netdev_priv\n", __func__);
		goto dropit;
	}

	if (!cpvnd->real_dev) {
		pr_err("in %s with no transmit device!\n", __func__);
		goto dropit;
	}

	if (drop_packet(skb, cpvnd)) {
		if (net_ratelimit())
			netdev_warn(dev, "In %s, DROPPED packet with source ip %pI4n dest %pI4n\n",
						__func__, &ip_hdr(skb)->saddr, &ip_hdr(skb)->daddr);
		goto dropit;
	}
	netdev_dbg(dev, "In %s, not dropping skb with source ip %pI4n\n",
		   __func__, &ip_hdr(skb)->saddr);
	skb_orphan(skb);
	skb->dev = cpvnd->real_dev;
	dev_queue_xmit(skb);
	/* other drivers seem to return NETDEV_TX_OK */
	return NET_XMIT_SUCCESS;
dropit:
	kfree_skb(skb);
	return NET_XMIT_DROP;
}

static void cpvnd_remove(struct cpvnd *cpvnd);
static void cpvnd_uninit(struct net_device *dev)
{
	struct cpvnd *cpvnd;

	netdev_dbg(dev, "In %s\n", __func__);
	if (!dev)
		return;

	cpvnd = netdev_priv(dev);
	if (!cpvnd)
		return;

	cpvnd_remove(cpvnd);
	return;
}

static int cpvnd_get_iflink(const struct net_device *dev)
{
	struct cpvnd *cpvnd = netdev_priv(dev);

	if (cpvnd && cpvnd->real_dev)
		return cpvnd->real_dev->ifindex;

	return -ENODEV;
}

static const struct net_device_ops cpvnd_ops = {
	.ndo_uninit     = cpvnd_uninit,
	.ndo_start_xmit = cpvnd_start_xmit,
	.ndo_get_iflink = cpvnd_get_iflink,
};

static void cpvnd_setup(struct net_device *dev)
{
	netdev_dbg(dev, "In %s\n", __func__);
	dev->netdev_ops = &cpvnd_ops;
	dev->mtu = 1500;
	dev->needed_headroom = 0;
	random_ether_addr(dev->dev_addr);

	/* Raw IP mode */
	dev->header_ops = NULL;  /* No header */
	dev->type = ARPHRD_RAWIP;
	dev->hard_header_len = 0;
	dev->flags &= ~(IFF_BROADCAST | IFF_MULTICAST);
}

/* We expect real_dev to exist */
static int cpvnd_newlink(struct net *src_net, struct net_device *dev,
			 struct nlattr *tb[], struct nlattr *data[], struct netlink_ext_ack *extack)
{
	int err;
	struct cpvnd *cpvnd = netdev_priv(dev);

	netdev_dbg(dev, "In %s\n", __func__);

	cpvnd->vnd = dev;
	if (!tb[IFLA_LINK]) {
		netdev_err(dev, "Missing interface index in netlink message in%s\n", __func__);
		return -EINVAL;
	}

	netdev_dbg(dev, "Attempting to get ifindex %d\n", nla_get_u32(tb[IFLA_LINK]));

	cpvnd->real_dev = dev_get_by_index(src_net, nla_get_u32(tb[IFLA_LINK]));
	if (!cpvnd->real_dev) {
		netdev_err(dev, "Failed to get device from index %d in %s\n", nla_get_u32(tb[IFLA_LINK]), __func__);
		return -ENODEV;
	}

	netdev_dbg(dev, "Got from ifindex %d\n", nla_get_u32(tb[IFLA_LINK]));

	err = netdev_rx_handler_register(cpvnd->real_dev, cpvnd_handle_frame, cpvnd);
	if (err < 0) {
		netdev_err(dev, "Failed to register rx_handler, err=%d\n", err);
		goto put_and_leave;
	}

	INIT_LIST_HEAD(&cpvnd->filters);
	err = register_netdevice(dev);
	if (err) {
		netdev_err(dev, "Failed to register netdevice in %s, err=%d\n", __func__, err);
		goto unregister_and_leave;
	}
	netdev_dbg(dev, "after netdev_register\n");
	return 0;
unregister_and_leave:
	netdev_info(dev, "Unregistering rx handler in %s\n", __func__);
	netdev_rx_handler_unregister(cpvnd->real_dev);
put_and_leave:
	dev_put(cpvnd->real_dev);
	cpvnd->real_dev = NULL;
	return err;
}

static void cpvnd_remove(struct cpvnd *cpvnd)
{
	struct cpvnd_filter_list *filter;

	if (!cpvnd)
		return;

	pr_debug("In %s\n", __func__);
	if (cpvnd->real_dev) {
		pr_debug("In %s Putting real_dev\n", __func__);
		netdev_rx_handler_unregister(cpvnd->real_dev);
		dev_put(cpvnd->real_dev);
		cpvnd->real_dev = NULL;
	}

	pr_debug("In %s, freefing filters\n", __func__);

	list_for_each_entry_rcu(filter, &cpvnd->filters, next) {
		list_del_rcu(&filter->next);
#if DEBUG
		pr_debug("%s is freeing this filter:\n", __func__);
		cpvnd_print_filter(&filter->cpvnd_filter);
#endif
		kfree_rcu(filter, rcu);
	}
}

/* Called when either when our interface is * removed  */
void cpvnd_dellink(struct net_device *dev, struct list_head *head)
{
	netdev_dbg(dev, "In %s\n", __func__);

	unregister_netdevice_queue(dev, head);
}

static int cpvnd_changelink(struct net_device *dev,
			    struct nlattr *tb[], struct nlattr *data[],struct netlink_ext_ack *extack)
{
	struct cpvnd_filter_list *filter, *pos;
	struct cpvnd *cpvnd = netdev_priv(dev);

	if (!data[IFLA_CPVND_FILTER]) {
		netdev_err(dev, "IFLA_CPVND_FILTER offset is null\n");
		return -EINVAL;
	}
	if (nla_len(data[IFLA_CPVND_FILTER]) != sizeof(struct cpvnd_filter)) {
		netdev_err(dev, "In %s, tb[IFLA_CPVND_FILTER] = %d bytes; sizeof(struct cpvnd_filter)=%zu\n", __func__, nla_len(data[IFLA_CPVND_FILTER]), sizeof(struct cpvnd_filter));
		return -EINVAL;
	}

	filter = kzalloc(sizeof(*filter), GFP_KERNEL);
	if (!filter) {
		return -ENOMEM;
	}

	netdev_dbg(dev, "in %s after allocating filter\n", __func__);

	/* Note the memcpy is limited by the nla attribute length, which should be
	 * exactly equal to the size of the cpvnd_filter. Since we've checked the
	 * max length above, we don't need to re-check here. */
	nla_memcpy(&filter->cpvnd_filter, data[IFLA_CPVND_FILTER], sizeof(struct cpvnd_filter));
#if DEBUG
	cpvnd_print_filter(&filter->cpvnd_filter);
#endif

	list_for_each_entry_rcu(pos, &cpvnd->filters, next) {
		if (memcmp(&filter->cpvnd_filter, &pos->cpvnd_filter, sizeof(struct cpvnd_filter)) == 0) {
			netdev_err(dev, "Filter already exists!\n");
			kfree(filter);
			return -EEXIST;
		}
	}
	list_add_tail_rcu(&filter->next, &cpvnd->filters);

#if DEBUG
	list_for_each_entry_rcu(pos, &cpvnd->filters, next)
		cpvnd_print_filter(&pos->cpvnd_filter);
#endif
	return 0;
}

static struct rtnl_link_ops cpvnd_link_ops = {
	.kind		= "cpvnd",
	.priv_size	= sizeof(struct cpvnd),
	.maxtype	= IFLA_CPVND_MAX,
	.setup		= cpvnd_setup,
	.newlink	= cpvnd_newlink,
	.dellink	= cpvnd_dellink,
	.changelink = cpvnd_changelink,
};

static int __init cpvnd_init_module(void)
{
	int err;

	err = register_netdevice_notifier(&cpvnd_notifier_block);
	if (err < 0) {
		pr_err("%s register netdevice notifier failed with %d\n", __func__, err);
		return err;
	}
	err = rtnl_link_register(&cpvnd_link_ops);
	if (err < 0) {
		pr_err("%s rtnl_link_register failed with %d\n", __func__, err);
		unregister_netdevice_notifier(&cpvnd_notifier_block);
	}
	return err;
}

static void __exit cpvnd_cleanup_module(void)
{
	rtnl_link_unregister(&cpvnd_link_ops);
	unregister_netdevice_notifier(&cpvnd_notifier_block);
}

module_init(cpvnd_init_module);
module_exit(cpvnd_cleanup_module);

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Kyle Swenson <<EMAIL>>");
