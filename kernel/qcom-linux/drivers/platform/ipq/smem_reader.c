#include <linux/module.h>
#include <linux/kernel.h>
#include <linux/init.h>
#include <linux/fs.h>
#include <linux/kobject.h>
#include <linux/sysfs.h>
#include <linux/io.h>

#define OCIMEM_OEM_ADDR 0x086006BC
static struct kobject *smem_reader_kobj;

static ssize_t mem_value_show(struct kobject *kobj, struct kobj_attribute *attr, char *buf)
{
    uint32_t value;
    void __iomem *mapped_addr;
    phys_addr_t phys_addr = OCIMEM_OEM_ADDR;

    mapped_addr = ioremap(phys_addr, sizeof(u32));
    if (!mapped_addr)
        return -ENOMEM;

    value = ioread32(mapped_addr);
    iounmap(mapped_addr);
    return sysfs_emit(buf, "0x%x\n", value);
}

static struct kobj_attribute mem_value_attr = __ATTR_RO(mem_value);

static int __init mem_reader_init(void)
{

    smem_reader_kobj = kobject_create_and_add("smem_reader", kernel_kobj);
    if (!smem_reader_kobj)
        return -ENOMEM;

    return sysfs_create_file(smem_reader_kobj, &mem_value_attr.attr);
}

static void __exit mem_reader_exit(void)
{
    sysfs_remove_file(smem_reader_kobj, &mem_value_attr.attr);
    kobject_put(smem_reader_kobj);
}

module_init(mem_reader_init);
module_exit(mem_reader_exit);
MODULE_LICENSE("GPL");

