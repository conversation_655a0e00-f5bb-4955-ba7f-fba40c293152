/*
 * drivers/platform/dock/dock_driver.c
 *
 * Copyright (C) 2021, Nordix Foundation
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License version 2 as published by
 * the Free Software Foundation.
 *
 * Driver to support RX30-PoE for the R1900
 */

#include <linux/kernel.h>
#include <linux/errno.h>
#include <linux/module.h>
#include <linux/slab.h>
#include <linux/types.h>
#include <linux/memory.h>
#include <linux/init.h>
#include <linux/platform_device.h>
#include <linux/of.h>
#include <linux/of_device.h>
#include <linux/gpio/consumer.h>
#include <linux/i2c.h>

#include "docks.h"

/* Match table for of_platform binding */
static const struct of_device_id dock_driver_of_match[] = {
	{ .compatible = "cp,dock-driver", },
	{},
};
MODULE_DEVICE_TABLE(of, dock_driver_of_match);

static const struct of_device_id rx30_poe_of_match[] = {
	{ .compatible = "cp,rx30-poe", },
	{},
};
MODULE_DEVICE_TABLE(of, rx30_poe_of_match);

static ssize_t sysfs_dock_serial_show(struct kobject *kobj, struct kobj_attribute *attr, char *buf)
{
	struct device *device_data = container_of(kobj, struct device, kobj);
	struct platform_device *pdev = container_of(device_data,
						struct platform_device, dev);
	struct dock_platform_data *dock_platform_data = platform_get_drvdata(pdev);
	return snprintf(buf, DOCK_SERIAL_NUMBER_LEN, "%s\n",
					dock_platform_data->data.serial);
}

static struct kobj_attribute dock_serial_attribute = __ATTR(dock_serial, 0440, sysfs_dock_serial_show, NULL);

static int parse_eeprom(struct i2c_client *eeprom, struct eeprom_data *data)
{
	ssize_t bytes_read;
	int i;
	uint8_t eeprom_data[256];
	uint16_t checksum;

	if (!eeprom) {
		return -ENODEV;
	}
	memset(eeprom_data, 0, 256);

	bytes_read = i2c_smbus_read_i2c_block_data_or_emulated(eeprom, 0x00, 255, eeprom_data);
	if (bytes_read < 0) {
		dev_info(&eeprom->dev, "Failed to read EEPROM, err=%ld\n", bytes_read);
		return bytes_read;
	}
	dev_info(&eeprom->dev, "Read %ld bytes of EEPROM contents!\n", bytes_read);

	if (eeprom_data[0] != 0x56) {
		dev_info(&eeprom->dev, "Expected start byte 0x56, found 0x%02X\n", eeprom_data[0]);
		return -EINVAL;
	}

	checksum = 0;
	for (i = 0; i < eeprom_data[1] && i < bytes_read; i++)
		checksum += eeprom_data[i];

	if (checksum & 0xFF) {
		dev_warn(&eeprom->dev, "Checksum failure, checksum should be zero  but is 0x%02X\n", checksum & 0xFF);
	}

	memset(data->product_id, 0, 3);
	memset(data->serial, 0, 15);
	data->product_id[0] = eeprom_data[9];
	data->product_id[1] = eeprom_data[10];

	memcpy(data->serial, &eeprom_data[3], 14);

	dev_info(&eeprom->dev, "Dock type: %s, serial: %s\n", data->product_id, data->serial);
	return 0;
}


/* Check to see if a particular device is present by reading an EEPROM that
 * might be on the  device */
int check_dock_match(struct platform_device *pdev)
{
	const char *model;
	struct dock_platform_data *dock_platform_data = platform_get_drvdata(pdev);
	struct device_node *of_node;
	struct device_node *i2c_devices = NULL;
	struct device_node *i2c_adapter_node = NULL;
	struct device_node *model_source_node = NULL;
	struct i2c_client *eeprom = NULL;
	struct i2c_adapter *i2c_bus = NULL;
	int ret = -ENODEV;

	of_node = pdev->dev.of_node;

	/* Get the phandle to the node for the eeprom */
	model_source_node = of_parse_phandle(of_node, "model_source", 0);
	if (!model_source_node) {
		dev_err(&pdev->dev, "Failed to find model_source property in %s\n", of_node->name);
		return -ENOENT;
	}

	i2c_devices = of_get_child_by_name(of_node, "i2c_devices");
	if (!i2c_devices) {
		dev_err(&pdev->dev, "Failed to find i2c_devices property in %s\n", of_node->name);
		ret = -EINVAL;
		goto put_of_nodes;
	}

	if (of_property_read_string(of_node, "model", &model)) {
		dev_err(&pdev->dev, "Failed to find model property in %s\n", of_node->name);
		ret = -EINVAL;
		goto put_of_nodes;
	}
	dev_info(&pdev->dev, "Trying to match model %s for %s\n", model, of_node->name);

	i2c_adapter_node = of_parse_phandle(i2c_devices, "host_bus", 0);
	if (!i2c_adapter_node) {
		dev_err(&pdev->dev, "Failed to find host_bus property\n");
		ret = -EINVAL;
		goto put_of_nodes;
	}

	i2c_bus = of_get_i2c_adapter_by_node(i2c_adapter_node);
	if (!i2c_bus) {
		dev_err(&pdev->dev, "Failed to get the i2c_adapter by node\n");
		ret = -EINVAL;
		goto put_of_nodes;
	}



	eeprom = of_i2c_register_device(i2c_bus, model_source_node);
	if (IS_ERR(eeprom)) {
		dev_warn(&pdev->dev, "Failed to register the EEPROM, ret=%ld\n", PTR_ERR(eeprom));
		ret = -ENODEV;
		goto adapter_put;

	}

	ret = parse_eeprom(eeprom, &dock_platform_data->data);
	if (ret) {
		dev_info(&pdev->dev, "Failed to read EEPROM\n");
		ret = -EPROBE_DEFER;
		goto undo_it_all_and_leave;
	}

	if (dock_platform_data->detect_gpio) {
		ret = sysfs_create_file(&pdev->dev.kobj, &dock_serial_attribute.attr);
		if (ret) {
			dev_err(&pdev->dev, "Failed to create sysfs file\n");
			goto undo_it_all_and_leave;
		}
	}

	if (strcmp(dock_platform_data->data.product_id, model) == 0) {
		dev_info(&pdev->dev, "Model data in EEPROM matches expected");
		ret = 0;
	} else {
		ret = -ENODEV;
		dev_info(&pdev->dev, "Model data in EEPROM (%s) doesn't match expected (%s)\n",
				dock_platform_data->data.product_id, model);
	}

undo_it_all_and_leave:
	i2c_unregister_device(eeprom);
adapter_put:
	i2c_put_adapter(i2c_bus);
put_of_nodes:
	of_node_put(model_source_node);
	of_node_put(i2c_adapter_node);
	of_node_put(i2c_devices);
	return ret;
}

int instantiate_dock_platform_devices(struct dock_platform_data *dock)
{
	struct device_node *of_child = NULL;
	struct device_node *plat_devices = NULL;
	struct platform_device * platdev = NULL;
	size_t client_idx;

	if (dock->plat_devices) {
		dev_info(dock->dev, "Platform devices already initialized\n");
		return 0;
	}

	plat_devices = of_get_child_by_name(dock->dev->of_node, "platform_devices");
	if (!plat_devices) {
		dev_info(dock->dev, "Failed to find i2c_devices property in %s\n", dock->dev->of_node->name);
		return 0; /* This is okay, just means there aren't any devices to create */
	}

	dock->nplat_devices = of_get_child_count(plat_devices);
	dev_info(dock->dev, "Found %zu platform_devices\n", dock->nplat_devices);

	if (dock->nplat_devices == 0) {
		of_node_put(plat_devices);
		return 0;
	}

	dock->plat_devices = devm_kzalloc(dock->dev, sizeof(struct platform_device *) * dock->nplat_devices, GFP_KERNEL);
	if (dock->plat_devices == NULL) {
		of_node_put(plat_devices);
		return -ENOMEM;
	}

	client_idx = 0;
	for_each_child_of_node(plat_devices, of_child)  {
		dev_info(dock->dev, "Instantiating node %s\n", of_child->name);
		platdev = of_platform_device_create(of_child, of_child->name, dock->dev);
		if (platdev == NULL) {
			dev_warn(dock->dev, "Failed to create platform_device %s\n", of_child->name);
			continue;
		}


		dock->plat_devices[client_idx++] = platdev;
	}

	dev_info(dock->dev, "Registered %zu clients\n", client_idx);
	of_node_put(plat_devices);
	return 0;
}

void remove_dock_devices(struct dock_platform_data *dock)
{
	int i = 0;

	if (dock->plat_devices) {
		for (i = 0; i < dock->nplat_devices; i++) {
			if (dock->plat_devices[i]) {
				platform_device_unregister(dock->plat_devices[i]);
				of_node_clear_flag(dock->plat_devices[i]->dev.of_node, OF_POPULATED);
				dock->plat_devices[i] = NULL;
			}
		}
		devm_kfree(dock->dev, dock->plat_devices);
		dock->plat_devices = NULL;
		dock->nplat_devices = 0;
	}
	if (dock->i2c_clients) {
		for (i = 0; i < dock->nclients; i++) {
				if (dock->i2c_clients[i]) {
					i2c_unregister_device(dock->i2c_clients[i]);
				dock->i2c_clients[i] = NULL;
			}
		}
		devm_kfree(dock->dev, dock->i2c_clients);
		dock->i2c_clients = NULL;
		dock->nclients = 0;
	}
	if (dock->i2c_bus) {
		i2c_put_adapter(dock->i2c_bus);
		dock->i2c_bus = NULL;
	}


}
int instantiate_dock_i2c_devices(struct dock_platform_data *dock)
{
	struct device_node *i2c_adapter_node = NULL;
	struct device_node *of_child = NULL;
	struct device_node *i2c_devices = NULL;
	struct i2c_client *client = NULL;
	size_t client_idx;

	if (dock->i2c_clients) {
		dev_info(dock->dev, "dock i2c clients is non-null\n");
		return 0;
	}
	i2c_devices = of_get_child_by_name(dock->dev->of_node, "i2c_devices");
	if (!i2c_devices) {
		dev_err(dock->dev, "Failed to find i2c_devices property in %s\n", dock->dev->of_node->name);
		return -EINVAL;
	}

	i2c_adapter_node = of_parse_phandle(i2c_devices, "host_bus", 0);
	if (!i2c_adapter_node) {
		dev_err(dock->dev, "Failed to find host_bus property\n");
		of_node_put(i2c_devices);
		return -EINVAL;
	}

	dock->nclients = of_get_child_count(i2c_devices);
	dev_info(dock->dev, "Found %zu clients\n", dock->nclients);

	if (dock->nclients == 0) {
		of_node_put(i2c_adapter_node);
		of_node_put(i2c_devices);
		return 0;
	}

	dock->i2c_bus = of_get_i2c_adapter_by_node(i2c_adapter_node);
	if (!dock->i2c_bus) {
		dev_info(dock->dev, "Failed to get dock's i2c_bus\n");
		of_node_put(i2c_adapter_node);
		of_node_put(i2c_devices);
		return -EINVAL;
	}

	dock->i2c_clients = devm_kzalloc(dock->dev, sizeof(struct i2c_client *) * dock->nclients, GFP_KERNEL);
	if (dock->i2c_clients == NULL) {
		i2c_put_adapter(dock->i2c_bus);
		of_node_put(i2c_adapter_node);
		of_node_put(i2c_devices);
		return -ENOMEM;
	}

	client_idx = 0;
	for_each_child_of_node(i2c_devices, of_child)  {
		dev_info(dock->dev, "Instantiating node %s\n", of_child->name);
		client = of_i2c_register_device(dock->i2c_bus, of_child);
		if (IS_ERR(client)) {
			dev_err(dock->dev, "Failed to create an i2c client, err=%ld\n", PTR_ERR(client));
			client = NULL;
			continue;
		}

		dock->i2c_clients[client_idx++] = client;
	}

	dev_info(dock->dev, "Registered %zu clients\n", client_idx);

	of_node_put(i2c_adapter_node);
	of_node_put(i2c_devices);
	return 0;
}

/* Set up the detect-gpios and power-gpios */
int setup_dock_gpio(struct dock_platform_data *dock_platform)
{
	int ret = 0;
	dock_platform->detect_gpio = devm_gpiod_get(dock_platform->dev, "dock-detect", GPIOD_IN);
	if (IS_ERR(dock_platform->detect_gpio)) {
		dev_err(dock_platform->dev, "Failed to get the detect gpio, err=%ld\n", PTR_ERR(dock_platform->detect_gpio));
		dock_platform->detect_gpio = NULL;
	}

	if (dock_platform->detect_gpio) {
		gpiod_export(dock_platform->detect_gpio, 1);
		gpiod_export_link(dock_platform->dev, "dock_detect",  dock_platform->detect_gpio);
		if (gpiod_get_value_cansleep(dock_platform->detect_gpio)) {
			dev_info(dock_platform->dev, "GPIO indicates a dock is present\n");
			dock_platform->power_gpios = devm_gpiod_get_array(dock_platform->dev, "dock-power", GPIOD_OUT_HIGH);
		} else {
			dev_info(dock_platform->dev, "GPIO indicates no dock present\n");
			dock_platform->power_gpios = devm_gpiod_get_array(dock_platform->dev, "dock-power", GPIOD_OUT_LOW);
			ret = -ENODEV;
		}
		if (IS_ERR(dock_platform->power_gpios)) {
			dev_err(dock_platform->dev, "Failed to get the power GPIO, err=%ld\n", PTR_ERR(dock_platform->power_gpios));
			dock_platform->power_gpios = NULL;
			ret = -EINVAL;
		}
	}

	return ret;

}

/* Free up the detect-gpios and power-gpios */
void teardown_dock_gpio(struct dock_platform_data *dock_platform)
{

	if (dock_platform->detect_gpio) {
		sysfs_remove_link(&dock_platform->dev->kobj, "dock_detect");
		gpiod_unexport(dock_platform->detect_gpio);
		devm_gpiod_put(dock_platform->dev, dock_platform->detect_gpio);
		dock_platform->detect_gpio = NULL;
	        sysfs_remove_file(&dock_platform->dev->kobj, &dock_serial_attribute.attr);
	}

	if (dock_platform->power_gpios) {
		devm_gpiod_put_array(dock_platform->dev, dock_platform->power_gpios);
		dock_platform->power_gpios = NULL;
	}

}

int instantiate_dock_devices(struct dock_platform_data *dock)
{

	int ret = 0;
	struct platform_device *pdev = to_platform_device(dock->dev);

	ret =check_dock_match(pdev);
	if (ret != 0) {
		dev_err(dock->dev, " Device doesn't match expected!\n");
		return ret;
	}

	ret = instantiate_dock_i2c_devices(dock);
	if (ret) {
		dev_err(dock->dev, "Failed to instantiate the i2c devices\n");
		return ret;
	}
	ret = instantiate_dock_platform_devices(dock);

	if (ret) {
		dev_err(dock->dev, "Failed to instantiate the platform devices\n");
		return ret;
	}
	return 0;
}

static int dock_driver_probe(struct platform_device *pdev)
{
	const struct of_device_id *of_match;
	struct device_node *of_node;
	struct device *dev = &pdev->dev;
	struct  dock_platform_data *dock_platform;
	int ret = 0;
	of_match = of_match_device(dock_driver_of_match, &pdev->dev);
	if (!of_match) {
		dev_err(&pdev->dev, "Error: No device match found\n");
		return -ENODEV;
	}

	of_node = pdev->dev.of_node;

	dock_platform = devm_kzalloc(&pdev->dev, sizeof(*dock_platform), GFP_KERNEL);
	if (!dock_platform) {
		return -ENOMEM;
	}

	dock_platform->dev = &pdev->dev;

	platform_set_drvdata(pdev, dock_platform);

	ret = setup_dock_gpio(dock_platform);
	if (ret != 0) {
		dev_info(dev, "Returning %d from %s after GPIO setup\n", ret, __func__);
		return ret;
	}
	ret = check_dock_match(pdev);
	if (ret != 0) {
		dev_info(dev, "returning %d from %s\n", ret, __func__);
		teardown_dock_gpio(dock_platform);
		devm_kfree(&pdev->dev, dock_platform);
		return ret;
	}

	instantiate_dock_i2c_devices(dock_platform);
	instantiate_dock_platform_devices(dock_platform);


	return 0;

}
static int dock_driver_remove(struct platform_device *pdev)
{
	dev_info(&pdev->dev, "In %s @ %s:%d\n", __func__, __FILE__, __LINE__);

	sysfs_remove_file(&pdev->dev.kobj, &dock_serial_attribute.attr);

	return 0;
}
static struct platform_driver dock_driver = {
	.probe   = dock_driver_probe,
	.remove  = dock_driver_remove,
	.driver  = {
		.name  = "dock-driver",
		.of_match_table = dock_driver_of_match,
	},
};

module_platform_driver(dock_driver);
#if 0

static int rx30_poe_dock_remove(struct platform_device *pdev)
{
	dev_info(&pdev->dev, "In %s @ %s:%d\n", __func__, __FILE__, __LINE__);
	return 0;

}


static struct platform_driver rx30_poe_dock_driver = {
	.probe   = rx30_poe_dock_probe,
	.remove  = rx30_poe_dock_remove,
	.driver  = {
		.name  = "rx30-poe-dock-driver",
		.of_match_table = rx30_poe_of_match
	},
};
module_platform_driver(rx30_poe_dock_driver);
#endif


MODULE_AUTHOR("Kyle Swenson <<EMAIL>>");
MODULE_DESCRIPTION("Platform device driver for Cradlepoint Docks");
MODULE_LICENSE("GPL");
