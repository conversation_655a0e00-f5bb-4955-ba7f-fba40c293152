PERF-CFLAGS
PERF-GUI-VARS
PERF-VERSION-FILE
FEATURE-DUMP
/perf
perf-read-vdso32
perf-read-vdsox32
perf-help
perf-record
perf-report
perf-stat
perf-top
perf*.1
perf*.xml
perf*.html
common-cmds.h
perf.data
perf.data.old
output.svg
perf-archive
perf-with-kcore
tags
TAGS
cscope*
config.mak
config.mak.autogen
*-bison.*
*-flex.*
*.pyc
*.pyo
.config-detected
util/intel-pt-decoder/inat-tables.c
arch/*/include/generated/
trace/beauty/generated/
pmu-events/pmu-events.c
pmu-events/jevents
feature/
fixdep
libtraceevent-dynamic-list
