/*
 * Copyright (c) 2017-2020, The Linux Foundation. All rights reserved.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#include "ipq8074.dtsi"
#include "ipq8074-hk-cpu.dtsi"
#include "ipq8074-i2c2.dtsi"
#include <dt-bindings/gpio/cp-garnet.h>

/ {
	#address-cells = <0x2>;
	#size-cells = <0x2>;
	model = "Qualcomm Technologies, Inc. IPQ8074/AP-HK07";
	compatible = "qcom,ipq8074-ap-hk07", "qcom,ipq8074";
	interrupt-parent = <&intc>;
	qcom,board-id = <0x8 0x0>;
	qcom,pmic-id = <0x0 0x0 0x0 0x0>;


	aliases {
		serial0 = &blsp1_uart5;
		serial1 = &blsp1_uart3;
		sdhc1 = &sdhc_1; /* SDC1 eMMC slot */
		/*
		 * Aliases as required by u-boot
		 * to patch MAC addresses
		 */
		ethernet0 = "/soc/dp1";
		ethernet1 = "/soc/dp2";
		ethernet2 = "/soc/dp3";
		ethernet3 = "/soc/dp4";
		ethernet4 = "/soc/dp5";
		ethernet5 = "/soc/dp6";
	};

	chosen {
		bootargs = "root=/dev/ram0 rw init=/init";
		#ifdef __IPQ_MEM_PROFILE_256_MB__
		#ifdef CONFIG_CRYPTO_FIPS_CORSEC
			bootargs-append = " swiotlb=1 usbcore.autosuspend=-1 fips=1";
		#else
			bootargs-append = " swiotlb=1 usbcore.autosuspend=-1";
		#endif
		#else
		#ifdef CONFIG_CRYPTO_FIPS_CORSEC
			bootargs-append = " swiotlb=1 usbcore.autosuspend=-1 coherent_pool=2M fips=1";
		#else
			bootargs-append = " swiotlb=1 usbcore.autosuspend=-1 coherent_pool=2M";
		#endif
		#endif


#ifdef CONFIG_CP_RELEASE_BUILD
		consoleargs = "console=none";
#else
		consoleargs = "console=ttyMSM0,115200,n8";
#endif
	};
};

&tlmm {

	antman_pins: antman_pins {
		mux_0 {
			pins = "gpio5";
			function = "gpio";
			input;
		};
		mux_1 {
			pins = "gpio12";
			function = "gpio";
			output-high;
		};
	};

	btcoex_pins: btcoex_pins {
		mux_0 {
			pins = "gpio64";
			function = "pta1_1";
			drive-strength = <6>;
			bias-disable;
		};
		mux_1 {
			pins = "gpio65";
			function = "pta1_2";
			drive-strength = <6>;
			bias-disable;
		};
		mux_2 {
			pins = "gpio66";
			function = "pta1_0";
			drive-strength = <6>;
			bias-disable;
		};
	};
	bt_uart_pins: hsuart_pins {
		mux {
			pins = "gpio46", "gpio47", "gpio48", "gpio49";
			function = "blsp2_uart";
			drive-strength = <8>;
			bias-disable;
		};
	};
	pcie0_pins: pcie_pins {
		pcie0_rst {
			pins = "gpio58";
			function = "pcie0_rst";
			drive-strength = <8>;
			bias-pull-down;
		};
		pcie0_wake {
			pins = "gpio57";
			function = "pcie0_wake";
			drive-strength = <8>;
			bias-pull-up;
		};
	};
	mdio_pins: mdio_pinmux {
		mux_0 {
			pins = "gpio68";
			function = "mdc";
			drive-strength = <8>;
			bias-pull-up;
		};
		mux_1 {
			pins = "gpio69";
			function = "mdio";
			drive-strength = <8>;
			bias-pull-up;
		};
	};

	uart_pins: uart_pins {
		mux {
			pins = "gpio23", "gpio24";
			function = "blsp4_uart1";
			drive-strength = <8>;
			bias-disable;
		};
	};

	i2c_2_pins: i2c_2_pinmux {
		mux {
			pins = "gpio0", "gpio2";
			function = "blsp5_i2c";
			drive-strength = <8>;
		};
	};



	uniphy_pins: uniphy_pinmux {
		mux {
			pins = "gpio60";
			function = "rx2";
			bias-disable;
		};

		sfp_tx {
			pins = "gpio59";
			function = "gpio";
			drive-strength = <8>;
			bias-pull-down;
			output-low;
		};
	};
};

&mdio {
	#address-cells = <1>;
	#size-cells = <0>;

	pinctrl-0 = <&mdio_pins>;
	pinctrl-names = "default";
	phy-reset-gpio = <&tlmm 37 GPIO_ACTIVE_LOW>;
	phy0: ethernet-phy@0 {
		reg = <0>;
	};
	phy1: ethernet-phy@1 {
		reg = <1>;
	};
	phy2: ethernet-phy@2 {
		reg = <2>;
	};
	phy3: ethernet-phy@3 {
		reg = <3>;
	};
	phy4: ethernet-phy@4 {
		reg = <4>;
	};
	phy5: ethernet-phy@5 {
		reg = <28>;
	};
};
&soc {

	ess-instance {
		ess-switch@3a000000 {
			pinctrl-0 = <&uniphy_pins>;
			pinctrl-names = "default";
			switch_cpu_bmp = <0x1>;  /* cpu port bitmap */
			switch_lan_bmp = <0x3e>; /* lan port bitmap */
			switch_wan_bmp = <0x40>; /* wan port bitmap */
			switch_mac_mode = <0x0>; /* mac mode for uniphy instance0*/
			switch_mac_mode1 = <0xff>; /* mac mode for uniphy instance1*/
			switch_mac_mode2 = <0xe>; /* mac mode for uniphy instance2*/
			bm_tick_mode = <0>; /* bm tick mode */
			tm_tick_mode = <0>; /* tm tick mode */
			mdio-bus = <&mdio>;
			qcom,port_phyinfo {
				port@0 {
					port_id = <1>;
					phy_address = <0>;
				};
				port@1 {
					port_id = <2>;
					phy_address = <1>;
				};
				port@2 {
					port_id = <3>;
					phy_address = <2>;
				};
				port@3 {
					port_id = <4>;
					phy_address = <3>;
				};
				port@4 {
					port_id = <5>;
					phy_address = <4>;
				};
				port@5 {
					port_id = <6>;
					phy_i2c_address = <0x50>;
					phy-i2c-mode; /*i2c access phy */
				};
			};
			port_scheduler_resource {
				port@0 {
					port_id = <0>;
					ucast_queue = <0 143>;
					mcast_queue = <256 271>;
					l0sp = <0 35>;
					l0cdrr = <0 47>;
					l0edrr = <0 47>;
					l1cdrr = <0 7>;
					l1edrr = <0 7>;
				};
				port@1 {
					port_id = <1>;
					ucast_queue = <144 159>;
					mcast_queue = <272 275>;
					l0sp = <36 39>;
					l0cdrr = <48 63>;
					l0edrr = <48 63>;
					l1cdrr = <8 11>;
					l1edrr = <8 11>;
				};
				port@2 {
					port_id = <2>;
					ucast_queue = <160 175>;
					mcast_queue = <276 279>;
					l0sp = <40 43>;
					l0cdrr = <64 79>;
					l0edrr = <64 79>;
					l1cdrr = <12 15>;
					l1edrr = <12 15>;
				};
				port@3 {
					port_id = <3>;
					ucast_queue = <176 191>;
					mcast_queue = <280 283>;
					l0sp = <44 47>;
					l0cdrr = <80 95>;
					l0edrr = <80 95>;
					l1cdrr = <16 19>;
					l1edrr = <16 19>;
				};
				port@4 {
					port_id = <4>;
					ucast_queue = <192 207>;
					mcast_queue = <284 287>;
					l0sp = <48 51>;
					l0cdrr = <96 111>;
					l0edrr = <96 111>;
					l1cdrr = <20 23>;
					l1edrr = <20 23>;
				};
				port@5 {
					port_id = <5>;
					ucast_queue = <208 223>;
					mcast_queue = <288 291>;
					l0sp = <52 55>;
					l0cdrr = <112 127>;
					l0edrr = <112 127>;
					l1cdrr = <24 27>;
					l1edrr = <24 27>;
				};
				port@6 {
					port_id = <6>;
					ucast_queue = <224 239>;
					mcast_queue = <292 295>;
					l0sp = <56 59>;
					l0cdrr = <128 143>;
					l0edrr = <128 143>;
					l1cdrr = <28 31>;
					l1edrr = <28 31>;
				};
				port@7 {
					port_id = <7>;
					ucast_queue = <240 255>;
					mcast_queue = <296 299>;
					l0sp = <60 63>;
					l0cdrr = <144 159>;
					l0edrr = <144 159>;
					l1cdrr = <32 35>;
					l1edrr = <32 35>;
				};
			};
			port_scheduler_config {
				port@0 {
					port_id = <0>;
					l1scheduler {
						group@0 {
							sp = <0 1>; /*L0 SPs*/
							/*cpri cdrr epri edrr*/
							cfg = <0 0 0 0>;
						};
					};
					l0scheduler {
						group@0 {
							/*unicast queues*/
							ucast_queue = <0 4 8>;
							/*multicast queues*/
							mcast_queue = <256 260>;
							/*sp cpri cdrr epri edrr*/
							cfg = <0 0 0 0 0>;
						};
						group@1 {
							ucast_queue = <1 5 9>;
							mcast_queue = <257 261>;
							cfg = <0 1 1 1 1>;
						};
						group@2 {
							ucast_queue = <2 6 10>;
							mcast_queue = <258 262>;
							cfg = <0 2 2 2 2>;
						};
						group@3 {
							ucast_queue = <3 7 11>;
							mcast_queue = <259 263>;
							cfg = <0 3 3 3 3>;
						};
					};
				};
				port@1 {
					port_id = <1>;
					l1scheduler {
						group@0 {
							sp = <36>;
							cfg = <0 8 0 8>;
						};
						group@1 {
							sp = <37>;
							cfg = <1 9 1 9>;
						};
					};
					l0scheduler {
						group@0 {
							ucast_queue = <144>;
							ucast_loop_pri = <16>;
							mcast_queue = <272>;
							mcast_loop_pri = <4>;
							cfg = <36 0 48 0 48>;
						};
					};
				};
				port@2 {
					port_id = <2>;
					l1scheduler {
						group@0 {
							sp = <40>;
							cfg = <0 12 0 12>;
						};
						group@1 {
							sp = <41>;
							cfg = <1 13 1 13>;
						};
					};
					l0scheduler {
						group@0 {
							ucast_queue = <160>;
							ucast_loop_pri = <16>;
							mcast_queue = <276>;
							mcast_loop_pri = <4>;
							cfg = <40 0 64 0 64>;
						};
					};
				};
				port@3 {
					port_id = <3>;
					l1scheduler {
						group@0 {
							sp = <44>;
							cfg = <0 16 0 16>;
						};
						group@1 {
							sp = <45>;
							cfg = <1 17 1 17>;
						};
					};
					l0scheduler {
						group@0 {
							ucast_queue = <176>;
							ucast_loop_pri = <16>;
							mcast_queue = <280>;
							mcast_loop_pri = <4>;
							cfg = <44 0 80 0 80>;
						};
					};
				};
				port@4 {
					port_id = <4>;
					l1scheduler {
						group@0 {
							sp = <48>;
							cfg = <0 20 0 20>;
						};
						group@1 {
							sp = <49>;
							cfg = <1 21 1 21>;
						};
					};
					l0scheduler {
						group@0 {
							ucast_queue = <192>;
							ucast_loop_pri = <16>;
							mcast_queue = <284>;
							mcast_loop_pri = <4>;
							cfg = <48 0 96 0 96>;
						};
					};
				};
				port@5 {
					port_id = <5>;
					l1scheduler {
						group@0 {
							sp = <52>;
							cfg = <0 24 0 24>;
						};
						group@1 {
							sp = <53>;
							cfg = <1 25 1 25>;
						};
					};
					l0scheduler {
						group@0 {
							ucast_queue = <208>;
							ucast_loop_pri = <16>;
							mcast_queue = <288>;
							mcast_loop_pri = <4>;
							cfg = <52 0 112 0 112>;
						};
					};
				};
				port@6 {
					port_id = <6>;
					l1scheduler {
						group@0 {
							sp = <56>;
							cfg = <0 28 0 28>;
						};
						group@1 {
							sp = <57>;
							cfg = <1 29 1 29>;
						};
					};
					l0scheduler {
						group@0 {
							ucast_queue = <224>;
							ucast_loop_pri = <16>;
							mcast_queue = <292>;
							mcast_loop_pri = <4>;
							cfg = <56 0 128 0 128>;
						};
					};
				};
				port@7 {
					port_id = <7>;
					l1scheduler {
						group@0 {
							sp = <60>;
							cfg = <0 32 0 32>;
						};
						group@1 {
							sp = <61>;
							cfg = <1 33 1 33>;
						};
					};
					l0scheduler {
						group@0 {
							ucast_queue = <240>;
							ucast_loop_pri = <16>;
							mcast_queue = <296>;
							cfg = <60 0 144 0 144>;
						};
					};
				};
			};
		};
	};
	dp1 {
		device_type = "network";
		compatible = "qcom,nss-dp";
		qcom,id = <1>;
		reg = <0x3a001000 0x200>;
		qcom,mactype = <0>;
		local-mac-address = [000000000000];
		qcom,link-poll = <1>;
		qcom,phy-mdio-addr = <0>;
		phy-mode = "sgmii";
	};

	dp2 {
		device_type = "network";
		compatible = "qcom,nss-dp";
		qcom,id = <2>;
		reg = <0x3a001200 0x200>;
		qcom,mactype = <0>;
		local-mac-address = [000000000000];
		qcom,link-poll = <1>;
		qcom,phy-mdio-addr = <1>;
		phy-mode = "sgmii";
	};

	dp3 {
		device_type = "network";
		compatible = "qcom,nss-dp";
		qcom,id = <3>;
		reg = <0x3a001400 0x200>;
		qcom,mactype = <0>;
		local-mac-address = [000000000000];
		qcom,link-poll = <1>;
		qcom,phy-mdio-addr = <2>;
		phy-mode = "sgmii";
	};

	dp4 {
		device_type = "network";
		compatible = "qcom,nss-dp";
		qcom,id = <4>;
		reg = <0x3a001600 0x200>;
		qcom,mactype = <0>;
		local-mac-address = [000000000000];
		qcom,link-poll = <1>;
		qcom,phy-mdio-addr = <3>;
		phy-mode = "sgmii";
	};

	dp5 {
		device_type = "network";
		compatible = "qcom,nss-dp";
		qcom,id = <5>;
		reg = <0x3a001800 0x200>;
		qcom,mactype = <0>;
		local-mac-address = [000000000000];
		qcom,link-poll = <1>;
		qcom,phy-mdio-addr = <4>;
		phy-mode = "sgmii";
	};

	dp6 {
		device_type = "network";
		compatible = "qcom,nss-dp";
		qcom,id = <6>;
		reg = <0x3a001a00 0x200>;
		qcom,mactype = <0>;
		local-mac-address = [000000000000];
		qcom,link-poll = <1>;
		qcom,phy-mdio-addr = <28>;
		phy-mode = "sgmii";
	};

	phy@84000 {
		status = "ok";
	};

	phy@86000 {
		status = "ok";
	};

	nss-macsec1 {
		compatible = "qcom,nss-macsec";
		phy_addr = <0x1c>;
		phy_access_mode = <0>;
		mdiobus = <&mdio>;
	};

	cp_leds: cp_leds@0 {
		status = "ok";
		compatible = "gpio-leds";
		attn_red { label="attn-red"; linux,default-trigger="sync_blink"; gpios=<&tlmm 3 GPIO_ACTIVE_HIGH>;};
		vpn_blu { label="vpn-blu"; linux,default-trigger="sync_blink"; gpios=<&tlmm 1 GPIO_ACTIVE_HIGH>;};
		mc400_grn { label="mc400-grn"; linux,default-trigger="sync_blink"; gpios=<&tlmm 15 GPIO_ACTIVE_HIGH>;};
		mc400_red { label="mc400-red"; linux,default-trigger="sync_blink"; gpios=<&tlmm 9 GPIO_ACTIVE_HIGH>;};
		vpn_grn { label="vpn-grn"; linux,default-trigger="sync_blink"; gpios=<&tlmm 11 GPIO_ACTIVE_HIGH>;};
		modem_red { label="modem-red"; linux,default-trigger="sync_blink"; gpios=<&tlmm 4 GPIO_ACTIVE_HIGH>;};
		modem_grn { label="modem-grn"; linux,default-trigger="sync_blink"; gpios=<&tlmm 6 GPIO_ACTIVE_HIGH>;};
		led_wifi { label="led-wifi"; linux,default-trigger="sync_blink"; gpios=<&tlmm 10 GPIO_ACTIVE_HIGH>;};
		led_ss_3 { label="led-ss-3"; linux,default-trigger="sync_blink"; gpios=<&tlmm 22 GPIO_ACTIVE_HIGH>;};
		led_ss_2 { label="led-ss-2"; linux,default-trigger="sync_blink"; gpios=<&tlmm 50 GPIO_ACTIVE_HIGH>;};
		led_ss_1 { label="led-ss-1"; linux,default-trigger="sync_blink"; gpios=<&tlmm 51 GPIO_ACTIVE_HIGH>;};
		led_ss_0 { label="led-ss-0"; linux,default-trigger="sync_blink"; gpios=<&tlmm 52 GPIO_ACTIVE_HIGH>;};

	};

	cp_gpio: cp-gpio {
		status = "ok";
		compatible = "cp,generic-gpio";
		pinctrl-0 = <&btcoex_pins &antman_pins>;
		pinctrl-names = "default";

		hw-wdt-en { output-gpios = <&tlmm 14 GPIO_ACTIVE_HIGH>; };

		int1-pwr-en { output-gpios = <&tlmm 27 GPIO_ACTIVE_LOW>; };
		int1-esim-select {output-gpios = <&tlmm 8 GPIO_ACTIVE_LOW>;};
		int1-sim2-select { output-gpios = <&tlmm 43 GPIO_ACTIVE_HIGH>; };
		int1-sim1-detect { input-gpios = <&tlmm 31 GPIO_ACTIVE_HIGH>;  }; /* SIM1_DET_CPU */
		int1-sim2-detect { input-gpios = <&tlmm 30 GPIO_ACTIVE_HIGH>;  }; /* SIM2_DET_CPU */
		int1-pla { input-gpios = <&tlmm 16 GPIO_ACTIVE_HIGH>; };
		int1-fcpf { output-gpios = <&tlmm 25 GPIO_ACTIVE_HIGH>; init-active; };

		bt-reset { output-gpios = <&tlmm 21 GPIO_ACTIVE_LOW>; init-active; };
		bt-pwr-en { output-gpios = <&tlmm 12 GPIO_ACTIVE_LOW>;};
		bt-spi-select { output-gpios = <&tlmm 28 GPIO_ACTIVE_HIGH>; } ; /* Select UART */
		bt-connect-mode { output-gpios = <&tlmm 13 GPIO_ACTIVE_HIGH>; };

		usb1-passthrough-en { output-gpios = <&tlmm 26 GPIO_ACTIVE_HIGH>; init-active; };
		usb-hub-reset {output-gpios = <&tlmm 29 GPIO_ACTIVE_HIGH>;};
		usb1-5v-detect { input-gpios = <&tlmm 63 GPIO_ACTIVE_LOW>; }; /* USB_EXTP_DET */

		mdm1 {
			event = <CURRENT_LIMIT_EVT>;
			input-gpios = <&tlmm 44 GPIO_ACTIVE_LOW>;
			power-gpios = <&tlmm 42 GPIO_ACTIVE_LOW>;
		};

		usb1 {
			event = <CURRENT_LIMIT_EVT>;
			input-gpios = <&tlmm 55 GPIO_ACTIVE_LOW>;
			power-gpios = <&tlmm 61 GPIO_ACTIVE_LOW>;
		};

		reset-button {
			input-gpios = <&tlmm 67 GPIO_ACTIVE_LOW>;
			event = <RESET_BUTTON_EVT>;
		};

		 sim-door-detect {
			input-gpios = <&tlmm 32 GPIO_ACTIVE_HIGH>;
			event = <SIM_DOOR_EVT>;
		};

		 bt-detect {
			input-gpios = <&tlmm 5 GPIO_ACTIVE_LOW>;
			event = <BT_DETECT_EVT>;
		};

		 sfp-tx-fault {
			input-gpios = <&tlmm 62 GPIO_ACTIVE_HIGH>;
			event = <SFP_TXFLT_EVT>;
		};

	};
	usb2642_i2c: usb-i2c {
		status = "ok";
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "microchip,usb2642-i2c";

		clock-delay = <0x00>; /* results in about a 390kHz clock */

		/* Vendor, model and rev are used to match a specific block device.
		* All present entries need to match exactly.  If any of vendor, model and rev
		* aren't present, then it will match all devices.  */
		vendor = "Cradlepoint";
		model = "MC400 HS-SD/MMC";
		rev = "2.09";
		gpio-controller;
		#gpio-cells = <2>;

		i2c_devices {
			#address-cells = <1>;
			#size-cells = <0>;
			shuri_gpio: tca6408@20 {
				compatible = "ti,tca6408";
				reg = <0x20>;
				reset-gpios = <&usb2642_i2c 0 GPIO_ACTIVE_LOW>;
				gpio-controller;
				#gpio-cells = <2>;
				status = "ok";
			};
		};
		platform_devices {
			mc400_gpio: mc400-5gb-gpio {
				status = "ok";
				compatible = "cp,generic-gpio";
				acc1-esim-select { output-gpios = <&shuri_gpio 0 GPIO_ACTIVE_HIGH>; };
				acc1-sim2-select { output-gpios = <&shuri_gpio 2 GPIO_ACTIVE_HIGH>;};
				acc1-fcpf { output-gpios = <&shuri_gpio 1 GPIO_ACTIVE_LOW>; };
				acc1-pla { input-gpios = <&shuri_gpio 3 GPIO_ACTIVE_HIGH>;};
				acc1-sim1-detect { input-gpios = <&shuri_gpio 5 GPIO_ACTIVE_LOW>;};
				acc1-sim2-detect { input-gpios = <&shuri_gpio 4 GPIO_ACTIVE_LOW>;};
				acc1-pwr-en { output-gpios = <&shuri_gpio 6 GPIO_ACTIVE_HIGH>; init-active;};
			};
		};
	};
};

&blsp1_uart5 {
	pinctrl-0 = <&uart_pins>;
	pinctrl-names = "default";
	status = "ok";
};

&blsp1_spi1 { /* BLSP1 QUP1 */
	pinctrl-0 = <&spi_0_pins>;
	pinctrl-names = "default";
	cs-select = <0>;
	status = "ok";

	m25p80@0 {
		#address-cells = <1>;
		#size-cells = <1>;
		reg = <0>;
		compatible = "n25q128a11";
		linux,modalias = "m25p80", "n25q128a11";
		spi-max-frequency = <50000000>;
		use-default-sizes;
	};
};

&blsp1_uart3 {
	pinctrl-0 = <&bt_uart_pins>;
	pinctrl-names = "default";
	status = "ok";
};
#ifndef __IPQ_MEM_PROFILE_256_MB__
&nss_crypto {
	status = "ok";
};

&msm_imem {
	status = "disabled";
};
#endif

&ssphy_0 {
	status = "ok";
};

&qusb_phy_0 {
	status = "ok";
};

&ssphy_1 {
	status = "ok";
};

&qusb_phy_1 {
	status = "ok";
};

&usb3_0 {
	status = "ok";
};

&usb3_1 {
	status = "ok";
};

&cryptobam {
	status = "ok";
};

&crypto {
	status = "ok";
};

&blsp1_i2c2 {
	status = "disabled";
};

&blsp1_i2c3 {
	status = "disabled";
};

&i2c_2 {
	pinctrl-0 = <&i2c_2_pins>;
	pinctrl-names = "default";
	status = "ok";

	aw21024_leds: led-controller@30 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "awinic,aw21024";
		reg = <0x30>;
		status = "ok";
		enable-gpios = <&tlmm 7 GPIO_ACTIVE_HIGH>;

		led@0 {
			reg = <0>;
			label = "led-bar-0";
			linux,default-trigger="sync_blink";
		};

		led@1 {
			reg = <1>;
			label = "led-bar-1";
			linux,default-trigger="sync_blink";
		};

		led@2 {
			reg = <2>;
			label = "led-bar-2";
			linux,default-trigger="sync_blink";
			ti,led-module = <2>;
		};

		led@3 {
			reg = <3>;
			label = "led-bar-3";
			linux,default-trigger="sync_blink";
			ti,led-module = <3>;
		};

		led@4 {
			reg = <4>;
			label = "led-bar-4";
			linux,default-trigger="sync_blink";
			ti,led-module = <4>;
		};

		led@5 {
			reg = <5>;
			label = "led-bar-5";
			linux,default-trigger="sync_blink";
			ti,led-module = <5>;
		};

		led@6 {
			reg = <6>;
			label = "led-bar-6";
			linux,default-trigger="sync_blink";
			ti,led-module = <6>;
		};

		led@7 {
			reg = <7>;
			label = "led-bar-7";
			linux,default-trigger="sync_blink";
			ti,led-module = <7>;
		};

		led@8 {
			reg = <8>;
			label = "led-bar-8";
			linux,default-trigger="sync_blink";
		};

		led@9 {
			reg = <9>;
			label = "led-bar-9";
			linux,default-trigger="sync_blink";
		};

		led@10 {
			reg = <10>;
			label = "led-bar-10";
			linux,default-trigger="sync_blink";
		};

		led@11 {
			reg = <11>;
			label = "led-bar-11";
			linux,default-trigger="sync_blink";
		};

		led@12 {
			reg = <12>;
			label = "led-bar-12";
			linux,default-trigger="sync_blink";
		};

		led@13 {
			reg = <13>;
			label = "led-bar-13";
			linux,default-trigger="sync_blink";
		};

		led@14 {
			reg = <14>;
			label = "led-bar-14";
			linux,default-trigger="sync_blink";
		};

		led@15 {
			reg = <15>;
			label = "led-bar-15";
			linux,default-trigger="sync_blink";
		};

		led@16 {
			reg = <16>;
			label = "led-bar-16";
			linux,default-trigger="sync_blink";
		};

		led@17 {
			reg = <17>;
			label = "led-bar-17";
			linux,default-trigger="sync_blink";
		};

		led@18 {
			reg = <18>;
			label = "led-mc400-ss-3";
			linux,default-trigger="sync_blink";
		};

		led@19 {
			reg = <19>;
			label = "led-mc400-ss-2";
			linux,default-trigger="sync_blink";
		};

		led@20 {
			reg = <20>;
			label = "led-mc400-ss-1";
			linux,default-trigger="sync_blink";
		};

		led@21 {
			reg = <21>;
			label = "led-mc400-ss-0";
			linux,default-trigger="sync_blink";
		};

		led@22 {
			reg = <22>;
			label = "led-logo-0";
			linux,default-trigger="sync_blink";
		};

		led@23 {
			reg = <23>;
			label = "led-logo-1";
			linux,default-trigger="sync_blink";
		};

	};
	lp5024: led-controller@28 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "ti,lp5024";
		reg = <0x28>;
		status = "ok";
		enable-gpios = <&tlmm 7 GPIO_ACTIVE_HIGH>;

		led@0 {
			reg = <0>;
			label = "led-bar-0";
			linux,default-trigger="sync_blink";
			ti,led-module = <0>;
		};

		led@1 {
			reg = <1>;
			label = "led-bar-1";
			linux,default-trigger="sync_blink";
			ti,led-module = <1>;
		};

		led@2 {
			reg = <2>;
			label = "led-bar-2";
			linux,default-trigger="sync_blink";
			ti,led-module = <2>;
		};

		led@3 {
			reg = <3>;
			label = "led-bar-3";
			linux,default-trigger="sync_blink";
			ti,led-module = <3>;
		};

		led@4 {
			reg = <4>;
			label = "led-bar-4";
			linux,default-trigger="sync_blink";
			ti,led-module = <4>;
		};

		led@5 {
			reg = <5>;
			label = "led-bar-5";
			linux,default-trigger="sync_blink";
			ti,led-module = <5>;
		};

		led@6 {
			reg = <6>;
			label = "led-bar-6";
			linux,default-trigger="sync_blink";
			ti,led-module = <6>;
		};

		led@7 {
			reg = <7>;
			label = "led-bar-7";
			linux,default-trigger="sync_blink";
			ti,led-module = <7>;
		};

		led@8 {
			reg = <8>;
			label = "led-bar-8";
			linux,default-trigger="sync_blink";

			ti,led-module = <8>;
		};

		led@9 {
			reg = <9>;
			label = "led-bar-9";
			linux,default-trigger="sync_blink";
			ti,led-module = <9>;
		};

		led@10 {
			reg = <10>;
			label = "led-bar-10";
			linux,default-trigger="sync_blink";
			ti,led-module = <10>;
		};

		led@11 {
			reg = <11>;
			label = "led-bar-11";
			linux,default-trigger="sync_blink";
			ti,led-module = <11>;
		};

		led@12 {
			reg = <12>;
			label = "led-bar-12";
			linux,default-trigger="sync_blink";
			ti,led-module = <12>;
		};

		led@13 {
			reg = <13>;
			label = "led-bar-13";
			linux,default-trigger="sync_blink";
			ti,led-module = <13>;
		};

		led@14 {
			reg = <14>;
			label = "led-bar-14";
			linux,default-trigger="sync_blink";
			ti,led-module = <14>;
		};

		led@15 {
			reg = <15>;
			label = "led-bar-15";
			linux,default-trigger="sync_blink";
			ti,led-module = <15>;
		};

		led@16 {
			reg = <16>;
			label = "led-bar-16";
			linux,default-trigger="sync_blink";
			ti,led-module = <16>;
		};

		led@17 {
			reg = <17>;
			label = "led-bar-17";
			linux,default-trigger="sync_blink";
			ti,led-module = <17>;
		};

		led@18 {
			reg = <18>;
			label = "led-mc400-ss-3";
			linux,default-trigger="sync_blink";
			ti,led-module = <18>;
		};

		led@19 {
			reg = <19>;
			label = "led-mc400-ss-2";
			linux,default-trigger="sync_blink";
			ti,led-module = <19>;
		};

		led@20 {
			reg = <20>;
			label = "led-mc400-ss-1";
			linux,default-trigger="sync_blink";
			ti,led-module = <20>;
		};

		led@21 {
			reg = <21>;
			label = "led-mc400-ss-0";
			linux,default-trigger="sync_blink";
			ti,led-module = <21>;
		};

		led@22 {
			reg = <22>;
			label = "led-logo-0";
			linux,default-trigger="sync_blink";
			ti,led-module = <22>;
		};

		led@23 {
			reg = <23>;
			label = "led-logo-1";
			linux,default-trigger="sync_blink";
			ti,led-module = <23>;
		};

	};
};

&sdhc_1 {
	status = "okay";
	/delete-property/ mmc-hs400-1_8v;
	/delete-property/ mmc-hs400-enhanced-strobe;
	max-frequency = <192000000>;
};

&qpic_bam {
	status = "disabled";
};

&qpic_nand {
	status = "disabled";
};

&qpic_lcd {
	status = "disabled";
};

&qpic_lcd_panel {
	status = "disabled";
};

&ledc {
	status = "disabled";
};

&pcie0 {

	interrupts-extended = <&tlmm 57 IRQ_TYPE_LEVEL_HIGH>;
	interrupt-names = "wake_gpio";


	pinctrl-names = "default";

	status = "ok";

	perst-gpio = <&tlmm 58 1>;

	pcie0_rp {
		reg = <0 0 0 0 0>;
		#address-cells = <5>;
		#size-cells = <0>;

		status = "ok";
		qcom,mhi@0 {

			reg = <0 0 0 0 0 >;
			/* controller specific configuration */
			qcom,iommu-dma = "disabled";

			mhi_devices: mhi_devices {
				#address-cells = <1>;
				#size-cells = <0>;

				mhi_netdev_0: mhi_rmnet@0 {
					reg = <0x0>;
					mhi,chan = "IP_HW0";
					mhi,interface-name = "mhi_net_int1";
					mhi,mru = <0x4000>;
					mhi,chain-skb;
				};

				mhi_rmnet@1 {
					reg = <0x1>;
					mhi,chan = "IP_HW0_RSC";
					mhi,mru = <0x8000>;
					mhi,rsc-parent = <&mhi_netdev_0>;
				};

				mhi_netdev_2: mhi_rmnet@2 {
					reg = <0x2>;
					mhi,chan = "IP_SW0";
					mhi,interface-name = "mhi_swip_int1";
					mhi,mru = <0x4000>;
					mhi,disable-chain-skb;
				};

				mhi_qrtr {
					mhi,chan = "IPCR";
					qcom,net-id = <3>;
				};
			};

		};
	};
};

&pcie1 {
	status = "disabled";
};
&wifi0 {
	qcom,board_id = <0x290>;
};
&wifi1 {
	qcom,pta-priority = <0x0>; //  BLE lowest priority
	btcoex_support = <0x1>;
	wlan_prio_gpio = <64>;
	qcom,board_id = <0x290>;
};

