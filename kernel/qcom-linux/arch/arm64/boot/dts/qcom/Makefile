# SPDX-License-Identifier: GPL-2.0
dtb-$(CONFIG_ARCH_QCOM)	+= apq8016-sbc.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= apq8096-db820c.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq6018-cp01-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq6018-cp01-c3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq6018-db-cp01.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq6018-cp01-c4.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq6018-cp01-c5.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq6018-cp02-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq6018-cp03-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk01.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-db.hk02.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk01.c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk01.c3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk01.c4.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk01.c5.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-hk01.c6.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk02.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk06.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk07.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk08.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk09.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk09.wkk.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk10.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk10.c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk10.wkk.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk11.c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq8074-hk14.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-oak02.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-oak03.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-ac01.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-ac02.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-ac03.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-ac04.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-hk12.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-emulation.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-emulation-fbc.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-emulation-ssoc.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al01-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al03-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al03-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c4.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c5.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c6.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c7.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c8.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c9.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c10.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c11.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c12.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c13.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c14.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c15.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-al02-c16.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-db-al01-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-db-al01-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-db-al01-c3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-db-al02-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-db-al02-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq9574-db-al02-c3.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq5018-mp02.1.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq5018-mp03.1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.1-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.1-c3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-db-mp03.1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.3-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.3-c3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.3-c4.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.3-c5.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.4-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.4-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.5-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.5-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.6-c1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp03.6-c2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-mp05.1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= ipq5018-tb-mp04.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= devsoc-emulation.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= devsoc-emulation-fbc.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= devsoc-emulation-ssoc.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= devsoc-01.1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= devsoc-01.2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= devsoc-01.4.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8916-mtp.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8916-longcheer-l8150.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8916-samsung-a3u-eur.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8916-samsung-a5u-eur.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8992-bullhead-rev-101.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8994-angler-rev-101.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8996-mtp.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8998-asus-novago-tp370ql.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8998-hp-envy-x2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8998-lenovo-miix-630.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= msm8998-mtp.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sdm845-cheza-r1.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sdm845-cheza-r2.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sdm845-cheza-r3.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sdm845-db845c.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sdm845-mtp.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sdm850-lenovo-yoga-c630.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sm8150-mtp.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= qcs404-evb-1000.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= qcs404-evb-4000.dtb
dtb-$(CONFIG_ARCH_QCOM)	+= sirocco-p0.dtb

# Cradlepoint DTSs
dtb-$(CONFIG_ARCH_QCOM) += ipq5018-gamora.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq5018-polaris.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq6018-cmarvel.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq6018-mabarker.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq6018-nwing.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq6018-robin.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq6018-scarlet.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-iron.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-profx-odu.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-profx.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-spider.dtb
dtb-$(CONFIG_ARCH_QCOM) += ipq8074-vesuvius.dtb
#todo create new file for spiderman USB
dtb-$(CONFIG_ARCH_QCOM) += ipq5018-apollo.dtb
