/dts-v1/;
/* Copyright (c) 2018-2020, The Linux Foundation. All rights reserved.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */

#include "ipq5018.dtsi"
#include <dt-bindings/gpio/cp-garnet.h>

/ {
	#address-cells = <0x2>;
	#size-cells = <0x2>;
	model = "Qualcomm Technologies, Inc. IPQ5018/AP-MP03.5-C2";
	compatible = "qcom,ipq5018-ap-mp03.5-c2", "qcom,ipq5018-mp03.5-c2", "qcom,ipq5018";
	interrupt-parent = <&intc>;

	/delete-property/ MP_256;
	MP_512;

	aliases {
		sdhc1 = &sdhc_1; /* SDC1 eMMC slot */
		serial0 = &blsp1_uart1; /* MSM0 internal console */
		serial1 = &blsp1_uart2; /* MSM1 ble UART */
		i2c0 = &i2c_0;
		i2c1 = &i2c_1;
		ethernet0 = "/soc/dp1";
		ethernet1 = "/soc/dp2";
	};

	chosen {
		bootargs = "root=/dev/ram0 rw init=/init";
		#ifdef CONFIG_CRYPTO_FIPS_CORSEC
			bootargs-append = " swiotlb=1 coherent_pool=2M cnss2.skip_radio_bmap=2 fips=1";
		#else
			bootargs-append = " swiotlb=1 coherent_pool=2M cnss2.skip_radio_bmap=2";
		#endif
		stdout-path = "serial0";
#ifdef CONFIG_CP_RELEASE_BUILD
		consoleargs = "console=none";
#else
		consoleargs = "console=ttyMSM0,115200,n8";
#endif
	};

	reserved-memory {
	#ifdef __IPQ_MEM_PROFILE_256_MB__
	/*                   256 MB Profile
	 * +==========+==============+=========================+
	 * |          |              |                         |
	 * |  Region  | Start Offset |          Size           |
	 * |          |              |                         |
	 * +----------+--------------+-------------------------+
	 * |    NSS   |  0x40000000  |           8MB           |
	 * +----------+--------------+-------------------------+
	 * |   Linux  |  0x40800000  | Depends on total memory |
	 * +----------+--------------+-------------------------+
	 * |   uboot  |  0x4A600000  |           4MB           |
	 * +----------+--------------+-------------------------+
	 * |    SBL   |  0x4AA00000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |   smem   |  0x4AB00000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |    TZ    |  0x4AC00000  |           4MB           |
	 * +----------+--------------+-------------------------+
	 * |    Q6    |              |                         |
	 * |   code/  |  0x4B000000  |          20MB           |
	 * |   data   |              |                         |
	 * +----------+--------------+-------------------------+
	 * |  IPQ5018 |              |                         |
	 * |   data   |  0x4C400000  |          13MB           |
	 * +----------+--------------+-------------------------+
	 * |  IPQ5018 |              |                         |
	 * |  M3 Dump |  0x4D100000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |  IPQ5018 |              |                         |
	 * |   QDSS   |  0x4D200000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_1|              |                         |
	 * |   data   |  0x4D300000  |          13MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_1|              |                         |
	 * |  M3 Dump |  0x4E000000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_1|              |                         |
	 * |   QDSS   |  0x4E100000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_2|              |                         |
	 * |   data   |  0x4E200000  |          13MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_2|              |                         |
	 * |  M3 Dump |  0x4EF00000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_2|              |                         |
	 * |   QDSS   |  0x4F000000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |                                                   |
	 * |            Rest of the memory for Linux           |
	 * |                                                   |
	 * +===================================================+
	 */
		q6_mem_regions: q6_mem_regions@4B000000 {
			no-map;
			reg = <0x0 0x4B000000 0x0 0x4100000>;
		};

		q6_code_data: q6_code_data@4B000000 {
			no-map;
			reg = <0x0 0x4B000000 0x0 0x1400000>;
		};

		q6_ipq5018_data: q6_ipq5018_data@4C400000 {
			no-map;
			reg = <0x0 0x4C400000 0x0 0xD00000>;
		};

		m3_dump: m3_dump@4D100000 {
			no-map;
			reg = <0x0 0x4D100000 0x0 0x100000>;
		};

		q6_etr_region: q6_etr_dump@4D200000 {
			no-map;
			reg = <0x0 0x4D200000 0x0 0x100000>;
		};

		q6_qcn6122_data1: q6_qcn6122_data1@4D300000 {
			no-map;
			reg = <0x0 0x4D300000 0x0 0xD00000>;
		};

		m3_dump_qcn6122_1: m3_dump_qcn6122_1@4E000000 {
			no-map;
			reg = <0x0 0x4E000000 0x0 0x100000>;
		};

		q6_qcn6122_etr_1: q6_qcn6122_etr_1@4E100000 {
			no-map;
			reg = <0x0 0x4E100000 0x0 0x100000>;
		};

		q6_qcn6122_data2: q6_qcn6122_data2@4E200000 {
			no-map;
			reg = <0x0 0x4E200000 0x0 0xD00000>;
		};

		m3_dump_qcn6122_2: m3_dump_qcn6122_2@4EF00000 {
			no-map;
			reg = <0x0 0x4EF00000 0x0 0x100000>;
		};

		q6_qcn6122_etr_2: q6_qcn6122_etr_2@4F000000 {
			no-map;
			reg = <0x0 0x4F000000 0x0 0x100000>;
		};
	#else
	/*                 512MB/1GB Profiles
	 * +==========+==============+=========================+
	 * |          |              |                         |
	 * |  Region  | Start Offset |          Size           |
	 * |          |              |                         |
	 * +----------+--------------+-------------------------+
	 * |    NSS   |  0x40000000  |          16MB           |
	 * +----------+--------------+-------------------------+
	 * |   Linux  |  0x41000000  | Depends on total memory |
	 * +----------+--------------+-------------------------+
	 * |   uboot  |  0x4A600000  |           4MB           |
	 * +----------+--------------+-------------------------+
	 * |    SBL   |  0x4AA00000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |   smem   |  0x4AB00000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |    TZ    |  0x4AC00000  |           4MB           |
	 * +----------+--------------+-------------------------+
	 * |    Q6    |              |                         |
	 * |   code/  |  0x4B000000  |          20MB           |
	 * |   data   |              |                         |
	 * +----------+--------------+-------------------------+
	 * |  IPQ5018 |              |                         |
	 * |   data   |  0x4C400000  |          13MB           |
	 * +----------+--------------+-------------------------+
	 * |  IPQ5018 |              |                         |
	 * |  M3 Dump |  0x4D100000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |  IPQ5018 |              |                         |
	 * |   QDSS   |  0x4D200000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * |  IPQ5018 |              |                         |
	 * |  Caldb   |  0x4D300000  |           2MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_1|              |                         |
	 * |   data   |  0x4D500000  |          13MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_1|              |                         |
	 * |  M3 Dump |  0x4E200000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_1|              |                         |
	 * |   QDSS   |  0x4E300000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_1|              |                         |
	 * |  Caldb   |  0x4E400000  |           5MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_2|              |                         |
	 * |   data   |  0x4E900000  |          13MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_2|              |                         |
	 * |  M3 Dump |  0x4F600000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_2|              |                         |
	 * |   QDSS   |  0x4F700000  |           1MB           |
	 * +----------+--------------+-------------------------+
	 * | QCN6122_2|              |                         |
	 * |  Caldb   |  0x4F800000  |           5MB           |
	 * +----------+--------------+-------------------------+
	 * |                                                   |
	 * |            Rest of the memory for Linux           |
	 * |                                                   |
	 * +===================================================+
	 */
		q6_mem_regions: q6_mem_regions@4B000000 {
			no-map;
			reg = <0x0 0x4B000000 0x0 0x4D00000>;
		};

		// not used and contains a confusing typo
//		q6_code_data: q6_code_data@4B000000 {
//			no-map;
//			reg = <0x0 0x4B000000 0x0 01400000>;
//		};

		q6_ipq5018_data: q6_ipq5018_data@4C400000 {
			no-map;
			reg = <0x0 0x4C400000 0x0 0xD00000>;
		};

		m3_dump: m3_dump@4D100000 {
			no-map;
			reg = <0x0 0x4D100000 0x0 0x100000>;
		};

		q6_etr_region: q6_etr_dump@4D200000 {
			no-map;
			reg = <0x0 0x4D200000 0x0 0x100000>;
		};

		q6_caldb_region: q6_caldb_region@4D300000 {
			no-map;
			reg = <0x0 0x4D300000 0x0 0x200000>;
		};

		q6_qcn6122_data1: q6_qcn6122_data1@4D500000 {
			no-map;
			reg = <0x0 0x4D500000 0x0 0xD00000>;
		};

		m3_dump_qcn6122_1: m3_dump_qcn6122_1@4E200000 {
			no-map;
			reg = <0x0 0x4E200000 0x0 0x100000>;
		};

		q6_qcn6122_etr_1: q6_qcn6122_etr_1@4E300000 {
			no-map;
			reg = <0x0 0x4E300000 0x0 0x100000>;
		};

		q6_qcn6122_caldb_1: q6_qcn6122_caldb_1@4E400000 {
			no-map;
			reg = <0x0 0x4E400000 0x0 0x500000>;
		};

		q6_qcn6122_data2: q6_qcn6122_data2@4E900000 {
			no-map;
			reg = <0x0 0x4E900000 0x0 0xD00000>;
		};

		m3_dump_qcn6122_2: m3_dump_qcn6122_2@4F600000 {
			no-map;
			reg = <0x0 0x4F600000 0x0 0x100000>;
		};

		q6_qcn6122_etr_2: q6_qcn6122_etr_2@4F700000 {
			no-map;
			reg = <0x0 0x4F700000 0x0 0x100000>;
		};

		q6_qcn6122_caldb_2: q6_qcn6122_caldb_2@4F800000 {
			no-map;
			reg = <0x0 0x4F800000 0x0 0x500000>;
		};

	#endif
	};

	soc {
		serial@78af000 {
			status = "ok";
		};

		qpic_bam: dma@7984000{
			status = "ok";
		};

		nand: qpic-nand@79b0000 {
			status = "disabled";
		};

		mdio0: mdio@88000 {
			status = "ok";

			pinctrl-0 = <&mdio0_pins>;
			pinctrl-names = "default";
			ethernet-phy@0 {
				reg = <7>;
			};
		};

		mdio1: mdio@90000 {
			status = "ok";
			pinctrl-0 = <&mdio1_pins>;
			pinctrl-names = "default";
			phy-reset-gpio = <&tlmm 39 0>;

			ethernet-phy@0 {
				reg = <16>;
			};
		};

		ess-instance {
			num_devices = <0x1>;
			ess-switch@0x39c00000 {
				switch_mac_mode = <0xf>; /* mac mode for uniphy instance*/
				cmnblk_clk = "internal_96MHz"; /* cmnblk clk*/
				qcom,port_phyinfo {
					port@0 {
						port_id = <1>;
						phy_address = <7>;
						mdiobus = <&mdio0>;
					};
					port@1 {
						port_id = <2>;
						phy_address = <16>;
						mdiobus = <&mdio1>;
						port_mac_sel = "QGMAC_PORT";
					};
				};
				led_source@0 {
					source = <0>;
					mode = "normal";
					speed = "1000M";
					freq = "auto";
					blink_en = "disable";
					active = "low";
				};
				led_source@1 {
					source = <1>;
					mode = "normal";
					speed = "all";
					freq = "auto";
					blink_en = "disable";
					active = "high";
				};
				led_source@2 {
					source = <2>;
					mode = "normal";
					speed = "all";
					freq = "auto";
					blink_en = "enable";
					active = "high";
				};
			};
		};

		rx20_mc_dock: rx20-mc {
			#address-cells = <1>;
			#size-cells = <0>;
			compatible = "cp,dock-driver";
			model = "DC";
			model_source = <&rx20_mc_eeprom>;

			dock-detect-gpios = <&exp_gpio 17 GPIO_ACTIVE_LOW> ;  /* DUCK_PRESENTn */
			dock-power-gpios = <&exp_gpio 10 GPIO_ACTIVE_HIGH> ; /* DUCK_EN */

			i2c_devices {
				#address-cells = <2>;
				#size-cells = <1>;
				host_bus = <&i2c_1>;
				rx20_mc_eeprom: eeprom@50 {
					compatible = "atmel,24c02";
					reg = <0x50>;
				};
				rx20_mc_temp_sensor: temp-sensor@4c {
					compatible = "gmt,g781";
					reg = <0x4c>;
				};
				rx20_mc_gpio_expander: gpio@27 {
					compatible = "ti,tca6408"; /* PCA9534, but 6408 driver should work */
					status = "ok";
					reg = <0x27>;
					gpio-controller;
					reset-gpios = <&exp_gpio 0 GPIO_ACTIVE_LOW>;
					#gpio-cells = <2>;
				};
			};
			platform_devices {
				cp_gpio_rx20_mc: rx20-mc-gpio {
					compatible = "cp,generic-gpio";
					mdm1 { input-gpios = <&rx20_mc_gpio_expander 1 GPIO_ACTIVE_HIGH>; power-gpios = <&rx20_mc_gpio_expander 0 GPIO_ACTIVE_HIGH>; event = <CURRENT_LIMIT_EVT>;}; /* USB_FAULT_n, USB_PWR_EN */
					ftdi-reset { output-gpios = <&rx20_mc_gpio_expander 3 GPIO_ACTIVE_LOW>; }; /* FT232_RST */
					user-pwr-ovc-1-5v { input-gpios = <&rx20_mc_gpio_expander 4 GPIO_ACTIVE_LOW>; }; /* IO_100mA_FAULT_n */
					eeprom-write-protect { input-gpios = <&rx20_mc_gpio_expander 7 GPIO_ACTIVE_LOW>; }; /* EEPROM_WCn */
					user-gpio-1 {
						input-gpios = <&rx20_mc_gpio_expander 5 GPIO_ACTIVE_HIGH>; /* 2X3_GPIO1_3V3 */
						event = <CONFIGURABLE_GPIO_EVT>;
					};
					user-gpio-2 {
						input-gpios = <&rx20_mc_gpio_expander 6 GPIO_ACTIVE_HIGH>; /* 2X3_GPIO2_3V3 */
						event = <CONFIGURABLE_GPIO_EVT>;
					};
				};

				usb2642_i2c: usb-i2c {
					status = "ok";
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "microchip,usb2642-i2c";

					clock-delay = <0x00>; /* results in about a 390kHz clock */

					/* Vendor, model and rev are used to match a specific block device.
					* All present entries need to match exactly.  If any of vendor, model and rev
					* aren't present, then it will match all devices.  */
					vendor = "Cradlepoint";
					model = "MC400 HS-SD/MMC";
					rev = "2.09";
					gpio-controller;
					#gpio-cells = <2>;

					i2c_devices {
						#address-cells = <1>;
						#size-cells = <0>;
						shuri_gpio: tca6408@20 {
							compatible = "ti,tca6408";
							reg = <0x20>;
							reset-gpios = <&usb2642_i2c 0 GPIO_ACTIVE_LOW>;
							gpio-controller;
							#gpio-cells = <2>;
							status = "ok";
						};
					};
					platform_devices {
						mc400_gpio: mc400-5gb-gpio {
							status = "ok";
							compatible = "cp,generic-gpio";
							acc1-esim-select { output-gpios = <&shuri_gpio 0 GPIO_ACTIVE_HIGH>; };
							acc1-sim2-select { output-gpios = <&shuri_gpio 2 GPIO_ACTIVE_HIGH>;};
							acc1-fcpf { output-gpios = <&shuri_gpio 1 GPIO_ACTIVE_LOW>; };
							acc1-pla { input-gpios = <&shuri_gpio 3 GPIO_ACTIVE_HIGH>;};
							acc1-sim1-detect { input-gpios = <&shuri_gpio 5 GPIO_ACTIVE_LOW>;};
							acc1-sim2-detect { input-gpios = <&shuri_gpio 4 GPIO_ACTIVE_LOW>;};
							acc1-pwr-en { output-gpios = <&shuri_gpio 6 GPIO_ACTIVE_HIGH>; init-active;};
						};
					};
				};
			};
		};

		cp_gpio: cp-gpio {
			status = "ok";
			compatible = "cp,generic-gpio";
			enable-power-off-control;

			mpcie-disable { output-gpios = <&tlmm 0 GPIO_ACTIVE_HIGH>;  }; /* MPCIE_W_DISABLE */
			int1-sim2-select { output-gpios = <&tlmm 10 GPIO_ACTIVE_HIGH>;  }; /* SIM_SEL_A_1V8 */
			int1-esim-select { output-gpios = <&tlmm 11 GPIO_ACTIVE_LOW>;  }; /* SIM_SEL_B_1V8 */
			int1-reset { output-gpios = <&tlmm 1 GPIO_ACTIVE_HIGH>;  }; /* mPCIe_RSTN_1V8 */
			board-reset { output-gpios = <&tlmm 19 GPIO_ACTIVE_HIGH>;  }; /* RESET_MRB */
			hw-wdt { output-gpios = <&tlmm 35 GPIO_ACTIVE_HIGH>;  }; /* HW_WDT */
			hw-wdt-en { output-gpios = <&tlmm 44 GPIO_ACTIVE_HIGH>;}; /* WDT_OE */

			bt-reset { output-gpios = <&exp_gpio 4 GPIO_ACTIVE_LOW>; init-active; }; /* BT_RESETn */
			bt-connect-mode { output-gpios = <&exp_gpio 23 GPIO_ACTIVE_HIGH>; }; /* BT_RESETn */

			usb1-5v-detect { input-gpios = <&exp_gpio 9 GPIO_ACTIVE_LOW>;  }; /* USB_5V_DET */
			int1-pla { input-gpios = <&exp_gpio 48 GPIO_ACTIVE_HIGH>;  }; /* PLA33# */
			adc-36v-select { output-gpios = <&exp_gpio 15 GPIO_ACTIVE_HIGH>; init-active; }; /* ADC_36V_5V_SW */
			usb1-passthrough-en { output-gpios = <&exp_gpio 16 GPIO_ACTIVE_LOW>;  }; /* USB_SWITCH_1V8 */
			int1-pwr-en { output-gpios = <&exp_gpio 19 GPIO_ACTIVE_HIGH>;  }; /* PCIE_PWR_EN */

			usb-hub-reset { output-gpios = <&exp_gpio 13 GPIO_ACTIVE_LOW>;  }; /* RESET_OUT_1V8 */
			user-gpio-pull-up-en { output-gpios = <&exp_gpio 14 GPIO_ACTIVE_LOW>; init-active; }; /* 2x2_GPIO_EN */
			pwr-connector-user-output { output-gpios = <&exp_gpio 21 GPIO_ACTIVE_HIGH>; }; /* 2X2_OUT */
			pwr-connector-user-pull-up-en { output-gpios = <&exp_gpio 22 GPIO_ACTIVE_LOW>; }; /* GPIO_PH_ENABLE */
			pwr-connector-user-buffer-direction { output-gpios = <&exp_gpio 20 GPIO_ACTIVE_HIGH>; }; /* 2X2_GPIO1_CTL */
			napa-reset { output-gpios = <&tlmm 39 GPIO_ACTIVE_HIGH>; init-active; }; /* NAPA RESET */

			pwr-off-clk { output-gpios = <&tlmm 2 GPIO_ACTIVE_HIGH>; power-off-clk; }; /* Power_Latch_CLK */
			flip-flop-data { output-gpios = <&tlmm 3 GPIO_ACTIVE_HIGH>; power-off-d; }; /* Power_Latch_data */

			int1-sim1-detect {
				input-gpios = <&exp_gpio 12 GPIO_ACTIVE_HIGH>; /* SIM1_DET_1*/
				event = <SIM_DOOR_EVT>;	
			};
			int1-sim2-detect {
				input-gpios = <&exp_gpio  11 GPIO_ACTIVE_HIGH>; /* SIM2_DET_1 */
				event = <SIM_DOOR_EVT>;
			};
			usb1 {
				input-gpios = <&exp_gpio 7 GPIO_ACTIVE_HIGH>; /* USB_CONN_OVC_DETECT */
				power-gpios = <&tlmm 43 GPIO_ACTIVE_HIGH>; /* USB_PWR_EN_1V8 */
				event = <CURRENT_LIMIT_EVT>;
			};
			pwr-connector-ignition-sense {
				input-gpios = <&tlmm 45 GPIO_ACTIVE_LOW>; /* IGN_sense_CPU */
				event = <CONFIGURABLE_GPIO_EVT>;
			};
			pwr-connector-user-output-en {
				input-gpios = <&tlmm 16 GPIO_ACTIVE_HIGH>; init-active; /* 2x2_GPIO1_CPU */
			};
			reset-button {
				input-gpios = <&tlmm 38 GPIO_ACTIVE_HIGH>; /* Power_Reset_button_1V8 */
				event = <RESET_BUTTON_EVT>;
			};

			};

		dp1 {
			device_type = "network";
			compatible = "qcom,nss-dp";
			clocks = <&gcc GCC_SNOC_GMAC0_AXI_CLK>;
			clock-names = "nss-snoc-gmac-axi-clk";
			qcom,id = <1>;
			reg = <0x39C00000 0x10000>;
			interrupts = <GIC_SPI 101 IRQ_TYPE_LEVEL_HIGH>;
			qcom,mactype = <2>;
			qcom,link-poll = <1>;
			qcom,phy-mdio-addr = <7>;
			mdio-bus = <&mdio0>;
			local-mac-address = [000000000000];
			phy-mode = "sgmii";
		};

		dp2 {
			device_type = "network";
			compatible = "qcom,nss-dp";
			clocks = <&gcc GCC_SNOC_GMAC1_AXI_CLK>;
			clock-names = "nss-snoc-gmac-axi-clk";
			qcom,id = <2>;
			reg = <0x39D00000 0x10000>;
			interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
			qcom,mactype = <2>;
			qcom,link-poll = <1>;
			qcom,phy-mdio-addr = <16>;
			mdio-bus = <&mdio1>;
			local-mac-address = [000000000000];
			phy-mode = "sgmii";
		};

		qcom,test@0 {
			status = "ok";
		};
	};

	thermal-zones {
		status = "ok";
	};
};

&blsp1_uart1 {
	pinctrl-0 = <&blsp0_uart_pins>;
	pinctrl-names = "default";
	status = "ok";
};

&blsp1_uart2 {
	pinctrl-0 = <&blsp1_uart_pins>;
	pinctrl-names = "default";
	status = "ok";
};

&sdhc_1 {
	pinctrl-0 = <&emmc_pins>;
	pinctrl-names = "default";
	qcom,clk-rates = <400000 25000000 50000000 100000000 \
			 192000000 384000000>;
	qcom,bus-speed-mode = "HS200_1p8v", "DDR_1p8v";
	qcom,nonremovable;
	status = "ok";
};

&tlmm {
	emmc_pins: emmc_pins {
		emmc_clk {
			pins = "gpio9";
			function = "sdc1_clk";
			drive-strength = <8>;
			bias-disable;
		};
		emmc_cmd {
			pins = "gpio8";
			function = "sdc1_cmd";
			drive-strength = <8>;
			bias-pull-up;
		};
		emmc_data_0 {
			pins = "gpio7";
			function = "sdc10";
			drive-strength = <8>;
			bias-disable;
		};
		emmc_data_1 {
			pins = "gpio6";
			function = "sdc11";
			drive-strength = <8>;
			bias-disable;
		};
		emmc_data_2 {
			pins = "gpio5";
			function = "sdc12";
			drive-strength = <8>;
			bias-disable;
		};
		emmc_data_3 {
			pins = "gpio4";
			function = "sdc13";
			drive-strength = <8>;
			bias-disable;
		};
	};

	blsp0_uart_pins: uart_pins {
		mux_0 {
			pins = "gpio20", "gpio21";
			function = "blsp0_uart0";
			bias-disable;
		};
	};

	blsp1_uart_pins: blsp1_uart_pins {
		mux_0 {
			pins = "gpio31", "gpio32", "gpio33", "gpio34";
			function = "blsp1_uart1";
			bias-disable;
		};
	};

	mdio0_pins: mdio_0_pinmux {
		mux_0 {
			pins = "gpio30";
			function = "led2";
			drive-strength = <8>;
			bias-pull-down;
		};

		mux_1 {
			pins = "gpio46";
			function = "led0";
			drive-strength = <8>;
			bias-pull-down;
		};
	};

	mdio1_pins: mdio_pinmux {
		mux_0 {
			pins = "gpio36";
			function = "mdc";
			drive-strength = <8>;
			bias-pull-up;
		};

		mux_1 {
			pins = "gpio37";
			function = "mdio";
			drive-strength = <8>;
			bias-pull-up;
		};
	};

	i2c_pins: i2c_pins { /*Pinmux for gamora */
		mux_0 {
			pins = "gpio25";
			function = "blsp2_i2c1";
			drive-strength = <8>;
			bias-pull-up;
		};

		mux_1 {
			pins = "gpio26";
			function = "blsp2_i2c1";
			drive-strength = <8>;
			bias-pull-up;
		};
	};

	i2c_pins2: i2c_pins2 { /* Pinmux def for nebula */
		mux_0 {
			pins = "gpio12";
			function = "blsp0_i2c";
			drive-strength = <8>;
			bias-disable;
		};
		mux_1 {
			pins = "gpio13";
			function = "blsp0_i2c";
			drive-strength = <8>;
			bias-disable;
		};
	};

};

&usb3 {
	status = "ok";
};

&eud {
	status = "ok";
};

&pcie_x1 {
	status = "disabled";
	perst-gpio = <&tlmm 18 1>;
};

&pcie_x2 {
	status = "disabled";
	perst-gpio = <&tlmm 15 1>;
};

&pcie_x1phy {
	status = "ok";
};

&pcie_x2phy {
	status = "disabled";
};


&tsens {
	status = "ok";
};

&q6v5_wcss {
	compatible = "qcom,ipq5018-q6-mpd";
	#address-cells = <1>;
	#size-cells = <1>;
	ranges;
	firmware = "IPQ5018/q6_fw.mdt";
	reg = <0x0cd00000 0x4040>,
		<0x1938000 0x8>,
		<0x193d204 0x4>;
	reg-names = "qdsp6",
			"tcsr-msip",
			"tcsr-q6";
	resets = <&gcc GCC_WCSSAON_RESET>,
			<&gcc GCC_WCSS_Q6_BCR>;

	reset-names = "wcss_aon_reset",
			"wcss_q6_reset";

	clocks = <&gcc GCC_Q6_AXIS_CLK>,
		<&gcc GCC_WCSS_ECAHB_CLK>,
		<&gcc GCC_Q6_AXIM_CLK>,
		<&gcc GCC_Q6_AXIM2_CLK>,
		<&gcc GCC_Q6_AHB_CLK>,
		<&gcc GCC_Q6_AHB_S_CLK>,
		<&gcc GCC_WCSS_AXI_S_CLK>;
	clock-names = "gcc_q6_axis_clk",
		"gcc_wcss_ecahb_clk",
		"gcc_q6_axim_clk",
		"gcc_q6_axim2_clk",
		"gcc_q6_ahb_clk",
		"gcc_q6_ahb_s_clk",
		"gcc_wcss_axi_s_clk";

	#ifdef __IPQ_MEM_PROFILE_256_MB__
		memory-region = <&q6_mem_regions>, <&q6_etr_region>;
	#else
		memory-region = <&q6_mem_regions>, <&q6_etr_region>,
				<&q6_caldb_region>;
	#endif

	qcom,rproc = <&q6v5_wcss>;
	qcom,bootargs_smem = <507>;
	boot-args = <0x1 0x4 0x3 0x0F 0x0 0x0>,
			<0x2 0x4 0x2 0x12 0x0 0x0>;
	status = "ok";
	q6_wcss_pd1: remoteproc_pd1@4ab000 {
		compatible = "qcom,ipq5018-wcss-ahb-mpd";
		reg = <0x4ab000 0x20>;
		reg-names = "rmb";
		firmware = "IPQ5018/q6_fw.mdt";
		m3_firmware = "IPQ5018/m3_fw.mdt";
		interrupts-extended = <&wcss_smp2p_in 8 0>,
					<&wcss_smp2p_in 9 0>,
					<&wcss_smp2p_in 12 0>,
					<&wcss_smp2p_in 11 0>;
		interrupt-names = "fatal",
					"ready",
					"spawn-ack",
					"stop-ack";

		resets = <&gcc GCC_WCSSAON_RESET>,
				<&gcc GCC_WCSS_BCR>,
				<&gcc GCC_CE_BCR>;
		reset-names = "wcss_aon_reset",
				"wcss_reset",
				"ce_reset";

		clocks = <&gcc GCC_WCSS_AHB_S_CLK>,
				<&gcc GCC_WCSS_ACMT_CLK>,
				<&gcc GCC_WCSS_AXI_M_CLK>;
		clock-names = "gcc_wcss_ahb_s_clk",
					"gcc_wcss_acmt_clk",
					"gcc_wcss_axi_m_clk";

		qcom,halt-regs = <&tcsr_q6_block 0xa000 0xd000 0x0>;

		qcom,smem-states = <&wcss_smp2p_out 8>,
					<&wcss_smp2p_out 9>,
					<&wcss_smp2p_out 10>;
		qcom,smem-state-names = "shutdown",
					"stop",
					"spawn";
	#ifdef __IPQ_MEM_PROFILE_256_MB__
		memory-region = <&q6_ipq5018_data>, <&m3_dump>,
				<&q6_etr_region>;
	#else
		memory-region = <&q6_ipq5018_data>, <&m3_dump>,
				<&q6_etr_region>, <&q6_caldb_region>;
	#endif

	};

	q6_wcss_pd2: remoteproc_pd2 {
		compatible = "qcom,ipq5018-wcss-pcie-mpd";
		firmware = "IPQ5018/q6_fw.mdt";
		m3_firmware = "qcn6122/m3_fw.mdt";
		interrupts-extended = <&wcss_smp2p_in 16 0>,
					<&wcss_smp2p_in 17 0>,
					<&wcss_smp2p_in 20 0>,
					<&wcss_smp2p_in 19 0>;
		interrupt-names = "fatal",
					"ready",
					"spawn-ack",
					"stop-ack";

		qcom,smem-states = <&wcss_smp2p_out 16>,
					<&wcss_smp2p_out 17>,
					<&wcss_smp2p_out 18>;
		qcom,smem-state-names = "shutdown",
					"stop",
					"spawn";
	#ifdef __IPQ_MEM_PROFILE_256_MB__
		memory-region = <&q6_qcn6122_data1>, <&m3_dump_qcn6122_1>,
				<&q6_qcn6122_etr_1>;
	#else
		memory-region = <&q6_qcn6122_data1>, <&m3_dump_qcn6122_1>,
				<&q6_qcn6122_etr_1>, <&q6_qcn6122_caldb_1>;
	#endif

	};

	q6_wcss_pd3: remoteproc_pd3 {
		compatible = "qcom,ipq5018-wcss-pcie-mpd";
		firmware = "IPQ5018/q6_fw.mdt";
		interrupts-extended = <&wcss_smp2p_in 24 0>,
					<&wcss_smp2p_in 25 0>,
					<&wcss_smp2p_in 28 0>,
					<&wcss_smp2p_in 27 0>;
		interrupt-names = "fatal",
					"ready",
					"spawn-ack",
					"stop-ack";

		qcom,smem-states = <&wcss_smp2p_out 24>,
					<&wcss_smp2p_out 25>,
					<&wcss_smp2p_out 26>;
		qcom,smem-state-names = "shutdown",
					"stop",
					"spawn";
	#ifdef	__IPQ_MEM_PROFILE_256_MB__
		memory-region = <&q6_qcn6122_data2>, <&m3_dump_qcn6122_2>,
				<&q6_qcn6122_etr_2>;
	#else
		memory-region = <&q6_qcn6122_data2>, <&m3_dump_qcn6122_2>,
				<&q6_qcn6122_etr_2>, <&q6_qcn6122_caldb_2>;
	#endif
	};
};

&wifi0 {
	/* IPQ5018 */
	qcom,multipd_arch;
	qcom,rproc = <&q6_wcss_pd1>;
	qcom,userpd-subsys-name = "q6v5_wcss_userpd1";
#ifdef __IPQ_MEM_PROFILE_256_MB__
	qcom,tgt-mem-mode = <2>;
#else
	qcom,tgt-mem-mode = <1>;
#endif
	qcom,board_id = <0x23>;
#ifdef __CNSS2__
	qcom,bdf-addr = <0x4C400000 0x4C400000 0x4C400000 0x0 0x0>;
	qcom,caldb-addr = <0x4D300000 0x4D300000 0 0 0>;
	qcom,caldb-size = <0x200000>;
	mem-region = <&q6_ipq5018_data>;
#else
	memory-region = <&q6_ipq5018_data>;
#endif
	status = "ok";
};


&wifi2 {
	/* QCN6122 5G */
	qcom,multipd_arch;
	qcom,userpd-subsys-name = "q6v5_wcss_userpd3";
	qcom,rproc = <&q6_wcss_pd3>;
#ifdef __IPQ_MEM_PROFILE_256_MB__
	qcom,tgt-mem-mode = <2>;
#else
	qcom,tgt-mem-mode = <1>;
#endif
	qcom,board_id = <0x50>;
#ifdef __CNSS2__
	qcom,bdf-addr = <0x4E900000 0x4E900000 0x4E200000 0x0 0x0>;
	qcom,caldb-addr = <0x4F800000 0x4F800000 0 0 0>;
	qcom,caldb-size = <0x500000>;
	mem-region = <&q6_qcn6122_data2>;
#else
	memory-region = <&q6_qcn6122_data2>;
#endif
	status = "ok";
};

&i2c_0 {
	pinctrl-0 = <&i2c_pins>;
	pinctrl-names = "default";
	status = "ok";

	temp-sensor@4c {
		compatible = "gmt,g781";
		reg = <0x4c>;
		status = "ok";
	};

	exp_gpio: gpio@22 {
		compatible = "ti,tca6424";
		status = "ok";
		reg = <0x22>;
		gpio-controller;
		reset-gpios = <&tlmm 29 GPIO_ACTIVE_LOW>;
		#gpio-cells = <2>;
	};

	lp5018_0: led-controller@29 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "ti,lp5018";
		reg = <0x29>;
		enable-gpios = <&exp_gpio 8 GPIO_ACTIVE_HIGH>; /* DRIVER_EN */
		status = "ok";

		led@0 {
			reg = <0>;
			label = "ncm-state";
			ti,led-module = <0>;
			linux,default-trigger="sync_blink";
			default-brightness = <210>;
		};

		led@1 {
			reg = <1>;
			label = "modem-grn";
			ti,led-module = <1>;
			linux,default-trigger="sync_blink";
			default-brightness = <210>;
		};

		led@2 {
			reg = <2>;
			label = "modem-red";
			ti,led-module = <2>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@3 {
			reg = <3>;
			label = "mc400-grn";
			ti,led-module = <3>;
			linux,default-trigger="sync_blink";
			default-brightness = <200>;
		};

		led@4 {
			reg = <4>;
			label = "mc400-red";
			ti,led-module = <4>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@5 {
			reg = <5>;
			label = "cellhealth-grn";
			ti,led-module = <5>;
			linux,default-trigger="sync_blink";
			default-brightness = <200>;
		};

		led@6 {
			reg = <6>;
			label = "cellhealth-red";
			ti,led-module = <6>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};
	};

	adc@49 {
		reset-gpios = <&exp_gpio 5 GPIO_ACTIVE_LOW>;
		compatible = "ads7924";
		reg = <0x49>;
		status = "ok";
	};
};

&i2c_1 {
	status = "ok";
	pinctrl-0 = <&i2c_pins2>;
	pinctrl-names = "default";
};

&lpass{
	status = "disabled";
};

&pcm {
	status = "disabled";
};

&pcm_lb {
	status = "disabled";
};

&bt {
	status = "disabled";
};

&dwc_0 {
	status = "ok";
};

&hs_m31phy_0 {
	status = "ok";
};
