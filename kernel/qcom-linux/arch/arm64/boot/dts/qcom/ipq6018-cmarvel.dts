/dts-v1/;
/*
 * Copyright (c) 2019, The Linux Foundation. All rights reserved.
 *
 * Permission to use, copy, modify, and/or distribute this software for any
 * purpose with or without fee is hereby granted, provided that the above
 * copyright notice and this permission notice appear in all copies.
 *
 * THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
 * WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
 * ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
 * WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
 * ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
 * OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.
 */


#include "ipq6018.dtsi"
#include "ipq6018-cp01.dtsi"
#include "ipq6018-cpr-regulator.dtsi"
#include <dt-bindings/gpio/cp-garnet.h>


/ {
	model = "Qualcomm Technologies, Inc. IPQ6018/AP-CP01-C1";

	/*
	 * +=========+==============+========================+
	 * |        |              |                         |
	 * | Region | Start Offset |          Size           |
	 * |        |              |                         |
	 * +--------+--------------+-------------------------+
	 * |        |              |                         |
	 * |        |              |                         |
	 * |        |              |                         |
	 * |        |              |                         |
	 * | Linux  |  0x41000000  |         139MB           |
	 * |        |              |                         |
	 * |        |              |                         |
	 * |        |              |                         |
	 * +--------+--------------+-------------------------+
	 * | TZ App |  0x49B00000  |           6MB           |
	 * +--------+--------------+-------------------------+
	 *
	 * From the available 145 MB for Linux in the first 256 MB,
	 * we are reserving 6 MB for TZAPP.
	 *
	 * Refer arch/arm64/boot/dts/qcom/qcom-ipq6018-memory.dtsi
	 * for memory layout.
	 */
	chosen {
		bootargs = "root=/dev/ram0 rw init=/init";
#ifdef CONFIG_CRYPTO_FIPS_CORSEC
		bootargs-append = " swiotlb=1 coherent_pool=2M fips=1";
#else
		bootargs-append = " swiotlb=1 coherent_pool=2M";
#endif

#ifdef CONFIG_CP_RELEASE_BUILD
		consoleargs = "console=none";
#else
		consoleargs = "console=ttyMSM0,115200,n8";
#endif
	};

	reserved-memory {
		mhi_region0: dma_pool0@52f00000 {
			compatible = "shared-dma-pool";
			no-map;
			reg = <0x0 0x52F00000 0x0 0x01800000>;
		};
	};

	aliases {
		serial0 = &blsp1_uart3; /* MSM0 internal console */
		serial1 = &blsp1_uart1; /* MSM1 external console */
		serial2 = &blsp1_uart2; /* MSM2 ble */
		serial3 = &blsp1_uart5; /* MSM3 gps */
		i2c0 = &i2c_2;
		i2c1 = &i2c_3;
		mdio-gpio0 = &mdio2;    /* MDIO over GPIO */
	};

};


&tlmm {
	i2c_2_pins: i2c_2_pins { /* binary i2c bus */
		mux {
			pins = "gpio73", "gpio74";
			function = "blsp3_i2c";
			bias-pull-up;
		};
	};
	i2c_3_pins: i2c_3_pins { /* main board i2c bus */
		mux {
			pins = "gpio55", "gpio56";
			function = "blsp4_i2c";
			bias-pull-up;
		};
	};
	ext_uart_pins: ext_uart_pins {
		mux {
			pins = "gpio40", "gpio41", "gpio38", "gpio39";
			function = "blsp0_uart";
			drive-strength = <8>;
			bias-disable;
		};
	};
	gps_uart_pins: gps_uart_pins {
		mux {
			pins = "gpio57", "gpio58";
			function = "blsp4_uart";
			drive-strength = <8>;
			bias-pull-down;
		};
	};

	mdio_pinmux {
		mux_3 {
			/* default was gpio77 which is not phy reset */
			status = "disabled";
		};
	};

	mdio_pins2: mdio_pinmux2 {
		mux_0 {
			pins = "gpio42";
			function = "gpio";
			drive-strength = <4>;
			bias-pull-up;
		};
		mux_1 {
			pins = "gpio43";
			function = "gpio";
			drive-strength = <4>;
			bias-pull-up;
		};
	};

	btcoex_pins: btcoex_pins {
		mux_0 {
			pins = "gpio51";
			function = "pta1_1";
			drive-strength = <6>;
			bias-disable;
		};
		mux_1 {
			pins = "gpio52";
			function = "pta1_2";
			drive-strength = <6>;
			bias-disable;
		};
		mux_2 {
			pins = "gpio53";
			function = "pta1_0";
			drive-strength = <6>;
			bias-disable;
		};
	};

	dock_pins: dock_pins {
		mux_0 { /* Dock detect */
			pins = "gpio15";
			function = "gpio";
			bias-pull-up;
		};
		mux_1 { /* Power enable */
			pins = "gpio16";
			function = "gpio";
			bias-pull-down;
		};
	};
};

&i2c_3 { /* Host I2C bus */
	pinctrl-0 = <&i2c_3_pins>;
	pinctrl-names = "default";
	status = "ok";

	aw21024_leds: led-controller@34 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "awinic,aw21024";
		reg = <0x34>;
		enable-gpios = <&tlmm  11 GPIO_ACTIVE_HIGH>; /* DRIVER_EN */

		led@0 {
			reg = <0>;
			label = "attn-red";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@1 {
			reg = <1>;
			label = "modem-grn";
			linux,default-trigger="sync_blink";
			default-brightness = <100>;
		};

		led@2 {
			reg = <2>;
			label = "modem-red";
			linux,default-trigger="sync_blink";
			default-brightness = <200>;
		};

		led@3 {
			reg = <3>;
			label = "led-wifi";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@4 {
			reg = <4>;
			label = "led-bt";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@5 {
			reg = <5>;
			label = "gps-blu";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@6 {
			reg = <6>;
			label = "led-ss-0";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@7 {
			reg = <7>;
			label = "led-ss-1";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@8 {
			reg = <8>;
			label = "led-ss-2";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@9 {
			reg = <9>;
			label = "led-ss-3";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@10 {
			reg = <10>;
			label = "modem-5g-grn";
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};
	};

	lp5024_0: led-controller@29 {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "ti,lp5024";
		reg = <0x29>;
		enable-gpios = <&tlmm  11 GPIO_ACTIVE_HIGH>; /* DRIVER_EN */

		led@0 {
			reg = <0>;
			label = "attn-red";
			ti,led-module = <0>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@1 {
			reg = <1>;
			label = "modem-grn";
			ti,led-module = <1>;
			linux,default-trigger="sync_blink";
			default-brightness = <100>;
		};

		led@2 {
			reg = <2>;
			label = "modem-red";
			ti,led-module = <2>;
			linux,default-trigger="sync_blink";
			default-brightness = <200>;
		};

		led@3 {
			reg = <3>;
			label = "led-wifi";
			ti,led-module = <3>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@4 {
			reg = <4>;
			label = "led-bt";
			ti,led-module = <4>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@5 {
			reg = <5>;
			label = "gps-blu";
			ti,led-module = <5>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@6 {
			reg = <6>;
			label = "led-ss-0";
			ti,led-module = <6>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@7 {
			reg = <7>;
			label = "led-ss-1";
			ti,led-module = <7>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@8 {
			reg = <8>;
			label = "led-ss-2";
			ti,led-module = <8>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@9 {
			reg = <9>;
			label = "led-ss-3";
			ti,led-module = <9>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};

		led@10 {
			reg = <10>;
			label = "modem-5g-grn";
			ti,led-module = <10>;
			linux,default-trigger="sync_blink";
			default-brightness = <255>;
		};
	};
	temp-sensor@4c {
		compatible = "gmt,g781";
		reg = <0x4c>;
	};
	adc@49 {
		reset-gpios = <&tlmm 18 GPIO_ACTIVE_LOW>;
		compatible = "ads7924";
		reg = <0x49>;
	};

};

&i2c_2 {
	pinctrl-0 = <&i2c_2_pins>;
	pinctrl-names = "default";
	status = "ok";

};
&sdhc_1 {
	status = "ok";
/*
	pinctrl-0;
	pinctrl-names;
	sd-ldo-gpios;
	cd-gpios;
*/
};


&qpic_pins {
	status = "disabled";
};

&qpic_bam {
	status = "disabled";
};
&qpic_nand {
	status = "disabled";
};
&spi_0 {
	status = "disabled";
};

&spi_1 {
	status = "disabled";
};

&qpic_lcd {
	status = "disabled";
};

&qpic_lcd_panel {
	status = "disabled";
};

&blsp1_uart1 {
	pinctrl-0 = <&ext_uart_pins>;
	pinctrl-names = "default";
	dmas = <&blsp_dma 0>,
		<&blsp_dma 1>;
	dma-names = "tx", "rx";
	status = "ok";
};

&blsp1_uart5 {
	pinctrl-0 = <&gps_uart_pins>;
	pinctrl-names = "default";
	dmas = <&blsp_dma 8>,
		<&blsp_dma 9>;
	dma-names = "tx", "rx";
	status = "ok";
};

&soc {
	gpio_keys {
		status = "disabled";
	};
	leds {
		status = "disabled";
	};

	cp_gpio: cp-gpio {
		status = "ok";
		compatible = "cp,generic-gpio";
		enable-power-off-control;

		user-gpio-1 {
				input-gpios = <&tlmm 2 GPIO_ACTIVE_HIGH>; /* 2X4_GPIO1_CPU */
				event = <CONFIGURABLE_GPIO_EVT>;
		};
		user-gpio-2 {
				input-gpios = <&tlmm 3 GPIO_ACTIVE_HIGH>; /* 2X4_GPIO2_CPU */
				event = <CONFIGURABLE_GPIO_EVT>;
		};
		user-gpio-3 {
				input-gpios = <&tlmm 10 GPIO_ACTIVE_HIGH>; /* 2X4_GPIO3_CPU */
				event = <CONFIGURABLE_GPIO_EVT>;
		};
		ignition-sense {
				input-gpios = <&tlmm 12 GPIO_ACTIVE_LOW>; /* IGN_sense_CPU */
				event = <CONFIGURABLE_GPIO_EVT>;
		};
		usb1 {
				input-gpios = <&tlmm 33 GPIO_ACTIVE_HIGH>; /* USB_CONN_OVC_DETECT */
				power-gpios = <&tlmm 37 GPIO_ACTIVE_HIGH>; /* USB_PWR_EN_1V8 */
				event = <CURRENT_LIMIT_EVT>;
		};
		reset-button {
				input-gpios = <&tlmm 77 GPIO_ACTIVE_HIGH>; /* Power_Reset_button_1V8 */
				event = <RESET_BUTTON_EVT>;
		};
		int1-sim1-detect {
			input-gpios = <&tlmm 31 GPIO_ACTIVE_HIGH>; /* SIM1_DET_1*/
			event = <SIM_DOOR_EVT>;	
		};
		int1-sim2-detect {
			input-gpios = <&tlmm 32 GPIO_ACTIVE_HIGH>; /* SIM2_DET_1 */
			event = <SIM_DOOR_EVT>;
		};

		dock-user-gpio-1 { output-gpios = <&tlmm 0 GPIO_ACTIVE_HIGH>;  }; /* DUCK_OUT_CPU */
		user-gpio-1-output-en { output-gpios = <&tlmm 1 GPIO_ACTIVE_HIGH>;  }; /* 2X4_GPIO1_CTL */
		user-gpio-2-output-en { output-gpios = <&tlmm 4 GPIO_ACTIVE_HIGH>;  }; /* 2X4_GPIO2_CTL */
		pwr-connector-user-output { output-gpios = <&tlmm 5 GPIO_ACTIVE_HIGH>;  }; /* 2X2_OUT_CPU */
		board-reset { output-gpios = <&tlmm 6 GPIO_ACTIVE_HIGH>;  }; /* RESET_MRB */
		user-gpio-3-output-en { output-gpios = <&tlmm 7 GPIO_ACTIVE_HIGH>;  }; /* 2X4_GPIO3_CTL */
		hw-wdt { output-gpios = <&tlmm 8 GPIO_ACTIVE_LOW>;  }; /* HW_WDT */
		hw-wdt-en { output-gpios = <&tlmm 9 GPIO_ACTIVE_HIGH>;  }; /* WDT_OE */
		user-gpio-pull-up-en { output-gpios = <&tlmm 13 GPIO_ACTIVE_LOW>;  }; /* 2X4_GPIO_EN */

		gps-pwr-clk { output-gpios = <&tlmm 17 GPIO_ACTIVE_HIGH>;  }; /* GPS_Power_OnOff_GPIO17 */
		gps-boot-mode-clk { output-gpios = <&tlmm 66 GPIO_ACTIVE_HIGH>;  }; /* GPS_Boot0_GPIO66 */
		gps-flip-flop-data { output-gpios = <&tlmm 19 GPIO_ACTIVE_HIGH>;  }; /* Riser_POWER_ON_IC_DATA_GPIO19 */
		gps-active-ant-clk { output-gpios = <&tlmm 20 GPIO_ACTIVE_HIGH>;  }; /* Active_Ant_CTL_GPIO20 */

		usb-hub-reset { output-gpios = <&tlmm 22 GPIO_ACTIVE_LOW>;  }; /* RESET_OUT_1V8 */
		switch-reset { output-gpios = <&tlmm 23 GPIO_ACTIVE_HIGH>; init-active;};
		adc-int { input-gpios = <&tlmm 25 GPIO_ACTIVE_HIGH>;  }; /* ADC_INT */
		adc-36v-select { output-gpios = <&tlmm 29 GPIO_ACTIVE_HIGH>;  }; /* ADC_36V_5V_SW */
		usb1-passthrough-en { output-gpios = <&tlmm 30 GPIO_ACTIVE_LOW>;  }; /* USB_SWITCH_1V8 */
		int1-pla { input-gpios = <&tlmm 48 GPIO_ACTIVE_LOW>;  }; /* PLA_GPIO48 */
		int1-sim2-select { output-gpios = <&tlmm 49 GPIO_ACTIVE_HIGH>;  }; /* SIM_SEL_A_1V8 */
		int1-esim-select { output-gpios = <&tlmm 50 GPIO_ACTIVE_LOW>;  }; /* SIM_SEL_B_1V8 */
		int1-pwr-en { output-gpios = <&tlmm 54 GPIO_ACTIVE_HIGH>;  }; /* PCIE_PWR_EN */
		int1-reset { output-gpios = <&tlmm 62 GPIO_ACTIVE_HIGH>;  }; /* mPCIe_RSTN_1V8 */
		int1-fcpf { output-gpios = <&tlmm 63 GPIO_ACTIVE_HIGH>; init-active; }; /* inverted */
		mpcie-disable { output-gpios = <&tlmm 61 GPIO_ACTIVE_HIGH>;  }; /* MPCIE_W_DISABLE */
		usb1-5v-detect { input-gpios = <&tlmm 34 GPIO_ACTIVE_LOW>;  }; /* USB_5V_DET */
		pcie-switch { output-gpios = <&tlmm 36 GPIO_ACTIVE_HIGH>;  }; /* GPIO_MUX_SEL */
		pwr-off-clk { output-gpios = <&tlmm 67 GPIO_ACTIVE_HIGH>; power-off-clk; }; /* Power_Latch_CLK */
		flip-flop-data { output-gpios = <&tlmm 68 GPIO_ACTIVE_HIGH>; power-off-d; }; /* Power_Latch_data */
		malibu-reset { output-gpios = <&tlmm 75 GPIO_ACTIVE_LOW>;  }; /* Malibu_RESET_GPIO75 */
		bt-connect-mode { output-gpios = <&tlmm 78 GPIO_ACTIVE_HIGH>;  }; /* BT_Recover_GPIO78 */
		bt-reset { output-gpios = <&tlmm 79 GPIO_ACTIVE_LOW>; init-active; }; /* BT_RESET_GPIO79 */

	};

	wifi: wifi@c000000 {
		qcom,pta-priority = <0x0>; //  BLE lowest priority
		btcoex_support = <0x1>;
		wlan_prio_gpio = <51>;
	};

	mdio: mdio@90000 {
		pinctrl-0 = <&mdio_pins>;
		pinctrl-names = "default";
		phy-reset-gpio = <&tlmm 75 0>;
		status = "ok";
		phy0: ethernet-phy@0 {
			reg = <8>;
		};
		phy1: ethernet-phy@1 {
			reg = <9>;
		};
		phy2: ethernet-phy@2 {
			reg = <10>;
		};
		phy3: ethernet-phy@3 {
			reg = <11>;
		};
		phy4: ethernet-phy@4 {
			reg = <12>;
		};
	};

	mdio2: mdio@1 {
		compatible = "virtual,mdio-gpio";
		#address-cells = <1>;
		#size-cells = <0>;
		gpios = <&tlmm 42 0 &tlmm 43 0>;
		pinctrl-0 = <&mdio_pins2>;
		pinctrl-names = "default";

		phy5: ethernet-phy@0 {
			device_type = "ethernet-phy";
			reg = <0>;
		};

	};

	dp1 {
		qcom,phy-mdio-addr = <8>;
	};

	dp2 {
		qcom,phy-mdio-addr = <9>;
	};

	dp3 {
		qcom,phy-mdio-addr = <10>;
	};

	dp4 {
		qcom,phy-mdio-addr = <11>;
	};

	dp5 {
		qcom,phy-mdio-addr = <12>;
		/delete-property/ qcom,link-poll;
	};

	ess-instance {
		num_devices = <0x2>;
		ess-switch@3a000000 {
			device_id = <0x0>;
			switch_cpu_bmp = <0x1>;  /* cpu port bitmap */
			switch_lan_bmp = <0x1e>; /* lan port bitmap */
			switch_wan_bmp = <0x20>; /* wan port bitmap */
			switch_inner_bmp = <0xc0>; /*inner port bitmap*/
			switch_mac_mode = <0xb>; /* mac mode for uniphy instance0*/
			switch_mac_mode1 = <0xf>; /* mac mode for uniphy instance1*/
			switch_mac_mode2 = <0xff>; /* mac mode for uniphy instance2*/
			bm_tick_mode = <0>; /* bm tick mode */
			tm_tick_mode = <0>; /* tm tick mode */
			qcom,port_phyinfo {
				port@0 {
					port_id = <1>;
					phy_address = <8>;
				};
				port@1 {
					port_id = <2>;
					phy_address = <9>;
				};
				port@2 {
					port_id = <3>;
					phy_address = <10>;
				};
				port@3 {
					port_id = <4>;
					phy_address = <11>;
				};
				port@4 {
					port_id = <5>;
					/delete-property/ phy_address;
					/delete-property/ port_mac_sel;
					forced-speed = <1000>;
					forced-duplex = <1>;
				};
			};
		};

		ess-switch1@1 {
			compatible = "qcom,ess-switch-qca83xx";
			device_id = <1>;
			switch_access_mode = "mdio";
			mdio-bus = <&mdio2>;
			switch_cpu_bmp = <0x40>;  /* cpu port bitmap */
			switch_lan_bmp = <0x3e>; /* lan port bitmap */
			switch_wan_bmp = <0x0>;  /* wan port bitmap */
			qca,ar8327-initvals = <
				0x00004 0x80080 	/* PAD0_MODE */
				0x00008 0x1000000 	/* PAD5_MODE */
				0x0000c 0x80 		/* PAD6_MODE */
				0x00010 0x2613a0 	/* PORT6 FORCE MODE*/
				0x000e4 0xaa545 	/* MAC_POWER_SEL */
				0x000e0 0xc74164de 	/* SGMII_CTRL */
				0x0007c 0x4e 		/* PORT0_STATUS */
				0x00094 0x0 		/* PORT6_STATUS */
				0x00624 0x007f7f7f	/* FORWARD_CONTROL */
			>;
			qcom,port_phyinfo {
				port@0 {
					port_id = <1>;
					phy_address = <0>;
				};
				port@1 {
					port_id = <2>;
					phy_address = <1>;
				};
				port@2 {
					port_id = <3>;
					phy_address = <2>;
				};
				port@3 {
					port_id = <4>;
					phy_address = <3>;
				};
				port@4 {
					port_id = <5>;
					phy_address = <4>;
				};
			};
		};
	};

	rx30_poe_dock: rx30-poe {
		#address-cells = <1>;
		#size-cells = <0>;
		compatible = "cp,dock-driver";
		model = "DA";
		model_source = <&rx30_poe_eeprom>;

		pinctrl-0 = <&dock_pins>;
		pinctrl-names = "default";

		dock-detect-gpios = <&tlmm 15 GPIO_ACTIVE_LOW> ;  /* Dock present */
		dock-power-gpios = <&tlmm 16 GPIO_ACTIVE_HIGH>; /* Dock power */

		i2c_devices {
			#address-cells = <2>;
			#size-cells = <1>;
			host_bus = <&i2c_2>;
			rx30_poe_eeprom: eeprom@50 {
				compatible = "atmel,24c02";
				reg = <0x50>;
			};
			pse: tps23880@22 {
				compatible = "ti,tps23880";
				reg = <0x22>;
				reset-gpios = <&tlmm 24 GPIO_ACTIVE_HIGH>;
			};
			temp-sensor@4c {
				compatible = "gmt,g781";
				reg = <0x4c>;
			};
		};
	};
	rx30_mc_dock: rx30-mc {
		#address-cells = <1>;
		#size-cells = <0>;
		model = "DB";
		model_source = <&rx30_mc_eeprom>;
		compatible = "cp,dock-driver";

		pinctrl-0 = <&dock_pins>;
		pinctrl-names = "default";

		dock-detect-gpios = <&tlmm 15 GPIO_ACTIVE_LOW> ;  /* This dock's presence detect on the host */
		dock-power-gpios = <&tlmm 16 GPIO_ACTIVE_HIGH>; /* This dock's power enable on the host */

		i2c_devices {
			#address-cells = <2>;
			#size-cells = <1>;
			host_bus = <&i2c_2>;
			rx30_mc_eeprom: eeprom@50 {
				compatible = "atmel,24c02";
				reg = <0x50>;
			};
			rx30_mc_gpio: gpio@21 {
				compatible = "ti,tca6408";
				reg = <0x21>;
				gpio-controller;
				reset-gpios = <&tlmm 24 GPIO_ACTIVE_LOW>;
				#gpio-cells = <2>;
			};
			temp-sensor@4c {
				compatible = "gmt,g781";
				reg = <0x4c>;
			};
		};
		platform_devices {
			cp_gpio_rx30_mc: rx30-mc-gpio {
				compatible = "cp,generic-gpio";

				usb-hub-reset { output-gpios = <&rx30_mc_gpio 3 GPIO_ACTIVE_LOW>;  };
				mdm1 {
					input-gpios = <&rx30_mc_gpio 1 GPIO_ACTIVE_LOW>;
					power-gpios = <&rx30_mc_gpio 0 GPIO_ACTIVE_HIGH>;
					event = <CURRENT_LIMIT_EVT>;
				};
			};
			usb2642_i2c: usb-i2c {
				status = "ok";
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "microchip,usb2642-i2c";

				clock-delay = <0x00>; /* results in about a 390kHz clock */

				/* Vendor, model and rev are used to match a specific block device.
				* All present entries need to match exactly.  If any of vendor, model and rev
				* aren't present, then it will match all devices.  */
				vendor = "Cradlepoint";
				model = "MC400 HS-SD/MMC";
				rev = "2.09";
				gpio-controller;
				#gpio-cells = <2>;

				i2c_devices {
					#address-cells = <1>;
					#size-cells = <0>;
					shuri_gpio: tca6408@20 {
						compatible = "ti,tca6408";
						reg = <0x20>;
						reset-gpios = <&usb2642_i2c 0 GPIO_ACTIVE_LOW>;
						gpio-controller;
						#gpio-cells = <2>;
						status = "ok";
					};
				};
				platform_devices {
					mc400_gpio: mc400-5gb-gpio {
						status = "ok";
						compatible = "cp,generic-gpio";
						acc1-esim-select { output-gpios = <&shuri_gpio 0 GPIO_ACTIVE_HIGH>; };
						acc1-sim2-select { output-gpios = <&shuri_gpio 2 GPIO_ACTIVE_HIGH>;};
						acc1-fcpf { output-gpios = <&shuri_gpio 1 GPIO_ACTIVE_LOW>; };
						acc1-pla { input-gpios = <&shuri_gpio 3 GPIO_ACTIVE_HIGH>;};
						acc1-sim1-detect { input-gpios = <&shuri_gpio 5 GPIO_ACTIVE_LOW>;};
						acc1-sim2-detect { input-gpios = <&shuri_gpio 4 GPIO_ACTIVE_LOW>;};
						acc1-pwr-en { output-gpios = <&shuri_gpio 6 GPIO_ACTIVE_HIGH>; init-active;};
					};
				};
			};
		};
	};
};

&pcie0 {
	pinctrl-names = "default";
	status = "ok";
	perst-gpio = <&tlmm 60 1>;

	pcie0_rp {
		reg = <0 0 0 0 0>;

		status = "ok";

		#address-cells = <5>;
		#size-cells = <0>;

		qcom,mhi@0 {
			reg = <0 0 0 0 0 >;

			/* controller specific configuration */
			qcom,iommu-dma = "disabled";


			mhi_devices: mhi_devices {
				#address-cells = <1>;
				#size-cells = <0>;

				mhi_netdev_0: mhi_rmnet@0 {
					reg = <0x0>;
					mhi,chan = "IP_HW0";
					mhi,interface-name = "mhi_net_int1";
					mhi,mru = <0x4000>;
					mhi,chain-skb;
				};

				mhi_netdev_2: mhi_rmnet@2 {
					reg = <0x2>;
					mhi,chan = "IP_SW_0";
					mhi,interface-name = "mhi_swip_int1";
					mhi,mru = <0x4000>;
					mhi,disable-chain-skb;
				};

				mhi_rmnet@1 {
					reg = <0x1>;
					mhi,chan = "IP_HW0_RSC";
					mhi,mru = <0x8000>;
					mhi,rsc-parent = <&mhi_netdev_0>;
				};

				mhi_qrtr {
					mhi,chan = "IPCR";
					qcom,net-id = <3>;
				};
			};

		};
	};
};
