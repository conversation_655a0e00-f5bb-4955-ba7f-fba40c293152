What:           /sys/class/dax/
Date:           May, 2016
KernelVersion:  v4.7
Contact:        <EMAIL>
Description:	Device DAX is the device-centric analogue of Filesystem
		DAX (CONFIG_FS_DAX).  It allows memory ranges to be
		allocated and mapped without need of an intervening file
		system.  Device DAX is strict, precise and predictable.
		Specifically this interface:

		1/ Guarantees fault granularity with respect to a given
		page size (pte, pmd, or pud) set at configuration time.

		2/ Enforces deterministic behavior by being strict about
		what fault scenarios are supported.

		The /sys/class/dax/ interface enumerates all the
		device-dax instances in the system. The ABI is
		deprecated and will be removed after 2020. It is
		replaced with the DAX bus interface /sys/bus/dax/ where
		device-dax instances can be found under
		/sys/bus/dax/devices/
