This ABI is deprecated and will be removed after 2021. It is
replaced with the batadv generic netlink family.

What:           /sys/class/net/<iface>/batman-adv/elp_interval
Date:           Feb 2014
Contact:        <PERSON><PERSON> <<EMAIL>>
Description:
                Defines the interval in milliseconds in which batman
                emits probing packets for neighbor sensing (ELP).

What:           /sys/class/net/<iface>/batman-adv/iface_status
Date:           May 2010
Contact:        <PERSON><PERSON> <<EMAIL>>
Description:
                Indicates the status of <iface> as it is seen by batman.

What:           /sys/class/net/<iface>/batman-adv/mesh_iface
Date:           May 2010
Contact:        <PERSON><PERSON> <mareklind<PERSON>@neomailbox.ch>
Description:
                The /sys/class/net/<iface>/batman-adv/mesh_iface file
                displays the batman mesh interface this <iface>
                currently is associated with.

What:           /sys/class/net/<iface>/batman-adv/throughput_override
Date:           Feb 2014
Contact:        <PERSON> <<EMAIL>>
description:
                Defines the throughput value to be used by B.A.T.M.A.N. V
                when estimating the link throughput using this interface.
                If the value is set to 0 then batman-adv will try to
                estimate the throughput by itself.
