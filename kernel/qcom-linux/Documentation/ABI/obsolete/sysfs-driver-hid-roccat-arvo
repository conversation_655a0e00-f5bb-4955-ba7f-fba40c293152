What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/arvo/roccatarvo<minor>/actual_profile
Date:		Januar 2011
Contact:	<PERSON> <<EMAIL>>
Description:	The integer value of this attribute ranges from 1-5.
		When read, this attribute returns the number of the actual
		profile which is also the profile that's active on device startup.
		When written this attribute activates the selected profile
		immediately.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/arvo/roccatarvo<minor>/button
Date:		Januar 2011
Contact:	<PERSON> <<EMAIL>>
Description:	The keyboard can store short macros with consist of 1 button with
		several modifier keys internally.
		When written, this file lets one set the sequence for a specific
		button for a specific profile. Button and profile numbers are
		included in written data. The data has to be 24 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/arvo/roccatarvo<minor>/info
Date:		Januar 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read, this file returns some info about the device like the
		installed firmware version.
		The size of the data is 8 bytes in size.
		This file is readonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/arvo/roccatarvo<minor>/key_mask
Date:		Januar 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	The keyboard lets the user deactivate 5 certain keys like the
		windows and application keys, to protect the user from the outcome
		of accidentally pressing them.
		The integer value of this attribute has bits 0-4 set depending
		on the state of the corresponding key.
		When read, this file returns the current state of the buttons.
		When written, the given buttons are activated/deactivated
		immediately.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/arvo/roccatarvo<minor>/mode_key
Date:		Januar 2011
Contact:	Stefan Achatz <<EMAIL>>
Description:	The keyboard has a condensed layout without num-lock key.
		Instead it uses a mode-key which activates a gaming mode where
		the assignment of the number block changes.
		The integer value of this attribute ranges from 0 (OFF) to 1 (ON).
		When read, this file returns the actual state of the key.
		When written, the key is activated/deactivated immediately.
Users:		http://roccat.sourceforge.net
