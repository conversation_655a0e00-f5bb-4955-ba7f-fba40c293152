This ABI is deprecated and will be removed after 2021. It is
replaced with the batadv generic netlink family.

What:           /sys/class/net/<mesh_iface>/mesh/aggregated_ogms
Date:           May 2010
Contact:        <PERSON><PERSON> <<EMAIL>>
Description:
                Indicates whether the batman protocol messages of the
                mesh <mesh_iface> shall be aggregated or not.

What:           /sys/class/net/<mesh_iface>/mesh/<vlan_subdir>/ap_isolation
Date:           May 2011
Contact:        <PERSON> <<EMAIL>>
Description:
                Indicates whether the data traffic going from a
                wireless client to another wireless client will be
                silently dropped. <vlan_subdir> is empty when referring
		to the untagged lan.

What:           /sys/class/net/<mesh_iface>/mesh/bonding
Date:           June 2010
Contact:        <PERSON> <<EMAIL>>
Description:
                Indicates whether the data traffic going through the
                mesh will be sent using multiple interfaces at the
                same time (if available).

What:           /sys/class/net/<mesh_iface>/mesh/bridge_loop_avoidance
Date:           November 2011
Contact:        <PERSON> <sw@simonwunder<PERSON>.de>
Description:
                Indicates whether the bridge loop avoidance feature
                is enabled. This feature detects and avoids loops
                between the mesh and devices bridged with the soft
                interface <mesh_iface>.

What:           /sys/class/net/<mesh_iface>/mesh/fragmentation
Date:           October 2010
Contact:        <PERSON>r <<EMAIL>>
Description:
                Indicates whether the data traffic going through the
                mesh will be fragmented or silently discarded if the
                packet size exceeds the outgoing interface MTU.

What:           /sys/class/net/<mesh_iface>/mesh/gw_bandwidth
Date:           October 2010
Contact:        Marek Lindner <<EMAIL>>
Description:
                Defines the bandwidth which is propagated by this
                node if gw_mode was set to 'server'.

What:           /sys/class/net/<mesh_iface>/mesh/gw_mode
Date:           October 2010
Contact:        Marek Lindner <<EMAIL>>
Description:
                Defines the state of the gateway features. Can be
                either 'off', 'client' or 'server'.

What:           /sys/class/net/<mesh_iface>/mesh/gw_sel_class
Date:           October 2010
Contact:        Marek Lindner <<EMAIL>>
Description:
                Defines the selection criteria this node will use
                to choose a gateway if gw_mode was set to 'client'.

What:           /sys/class/net/<mesh_iface>/mesh/hop_penalty
Date:           Oct 2010
Contact:        Linus Lüssing <<EMAIL>>
Description:
                Defines the penalty which will be applied to an
                originator message's tq-field on every hop.

What:		/sys/class/net/<mesh_iface>/mesh/isolation_mark
Date:		Nov 2013
Contact:	Antonio Quartulli <<EMAIL>>
Description:
		Defines the isolation mark (and its bitmask) which
		is used to classify clients as "isolated" by the
		Extended Isolation feature.

What:           /sys/class/net/<mesh_iface>/mesh/multicast_mode
Date:           Feb 2014
Contact:        Linus Lüssing <<EMAIL>>
Description:
                Indicates whether multicast optimizations are enabled
                or disabled. If set to zero then all nodes in the
                mesh are going to use classic flooding for any
                multicast packet with no optimizations.

What:           /sys/class/net/<mesh_iface>/mesh/network_coding
Date:           Nov 2012
Contact:        Martin Hundeboll <<EMAIL>>
Description:
                Controls whether Network Coding (using some magic
                to send fewer wifi packets but still the same
                content) is enabled or not.

What:           /sys/class/net/<mesh_iface>/mesh/orig_interval
Date:           May 2010
Contact:        Marek Lindner <<EMAIL>>
Description:
                Defines the interval in milliseconds in which batman
                sends its protocol messages.

What:           /sys/class/net/<mesh_iface>/mesh/routing_algo
Date:           Dec 2011
Contact:        Marek Lindner <<EMAIL>>
Description:
                Defines the routing procotol this mesh instance
                uses to find the optimal paths through the mesh.
