What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/actual_profile
Date:		December 2012
Contact:	<PERSON> <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. actual_profile holds number of actual profile.
		This value is persistent, so its value determines the profile
		that's active when the mouse is powered on next time.
		When written, the mouse activates the set profile immediately.
		The data has to be 3 bytes long.
		The mouse will reject invalid data.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/control
Date:		December 2012
Contact:	<PERSON> <<EMAIL>>
Description:	When written, this file lets one select which data from which
		profile will be	read next. The data has to be 3 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/info
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read, this file returns general data like firmware version.
		When written, the device can be reset.
		The data is 6 bytes long.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/macro
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store a macro with max 500 key/button strokes
		internally.
		When written, this file lets one set the sequence for a specific
		button for a specific profile. Button and profile numbers are
		included in written data. The data has to be 2082 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/profile_buttons
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split in settings and buttons.
		profile_buttons holds information about button layout.
		When written, this file lets one write the respective profile
		buttons back to the mouse. The data has to be 59 bytes long.
		The mouse will reject invalid data.
		Which profile to write is determined by the profile number
		contained in the data.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/profile_settings
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse can store 5 profiles which can be switched by the
		press of a button. A profile is split in settings and buttons.
		profile_settings holds information like resolution, sensitivity
		and light effects.
		When written, this file lets one write the respective profile
		settings back to the mouse. The data has to be 31 bytes long.
		The mouse will reject invalid data.
		Which profile to write is determined by the profile number
		contained in the data.
		Before reading this file, control has to be written to select
		which profile to read.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/sensor
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	The mouse has a tracking- and a distance-control-unit. These
		can be activated/deactivated and the lift-off distance can be
		set. The data has to be 6 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/talk
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	Used to active some easy* functions of the mouse from outside.
		The data has to be 16 bytes long.
		This file is writeonly.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/tcu
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	When written a calibration process for the tracking control unit
		can be initiated/cancelled. Also lets one read/write sensor
		registers.
		The data has to be 4 bytes long.
Users:		http://roccat.sourceforge.net

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/konepure/roccatkonepure<minor>/tcu_image
Date:		December 2012
Contact:	Stefan Achatz <<EMAIL>>
Description:	When read the mouse returns a 30x30 pixel image of the
		sampled underground. This works only in the course of a
		calibration process initiated with tcu.
		The returned data is 1028 bytes in size.
		This file is readonly.
Users:		http://roccat.sourceforge.net
