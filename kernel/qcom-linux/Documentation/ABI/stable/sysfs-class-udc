What:		/sys/class/udc/<udc>/a_alt_hnp_support
Date:		June 2011
KernelVersion:	3.1
Contact:	<PERSON> <<EMAIL>>
Description:
		Indicates if an OTG A-Host supports HNP at an alternate port.
Users:

What:		/sys/class/udc/<udc>/a_hnp_support
Date:		June 2011
KernelVersion:	3.1
Contact:	<PERSON> <<EMAIL>>
Description:
		Indicates if an OTG A-Host supports HNP at this port.
Users:

What:		/sys/class/udc/<udc>/b_hnp_enable
Date:		June 2011
KernelVersion:	3.1
Contact:	<PERSON> <<EMAIL>>
Description:
		Indicates if an OTG A-Host enabled HNP support.
Users:

What:		/sys/class/udc/<udc>/current_speed
Date:		June 2011
KernelVersion:	3.1
Contact:	<PERSON> <<EMAIL>>
Description:
		Indicates the current negotiated speed at this port.
Users:

What:		/sys/class/udc/<udc>/is_a_peripheral
Date:		June 2011
KernelVersion:	3.1
Contact:	<PERSON> <<EMAIL>>
Description:
		Indicates that this port is the default Host on an OTG session
		but HNP was used to switch roles.
Users:

What:		/sys/class/udc/<udc>/is_otg
Date:		June 2011
KernelVersion:	3.1
Contact:	Felipe Balbi <<EMAIL>>
Description:
		Indicates that this port support OTG.
Users:

What:		/sys/class/udc/<udc>/maximum_speed
Date:		June 2011
KernelVersion:	3.1
Contact:	Felipe Balbi <<EMAIL>>
Description:
		Indicates the maximum USB speed supported by this port.
Users:

What:		/sys/class/udc/<udc>/soft_connect
Date:		June 2011
KernelVersion:	3.1
Contact:	Felipe Balbi <<EMAIL>>
Description:
		Allows users to disconnect data pullup resistors thus causing a
		logical disconnection from the USB Host.
Users:

What:		/sys/class/udc/<udc>/srp
Date:		June 2011
KernelVersion:	3.1
Contact:	Felipe Balbi <<EMAIL>>
Description:
		Allows users to manually start Session Request Protocol.
Users:

What:		/sys/class/udc/<udc>/state
Date:		June 2011
KernelVersion:	3.1
Contact:	Felipe Balbi <<EMAIL>>
Description:
		Indicates current state of the USB Device Controller. Valid
		states are: 'not-attached', 'attached', 'powered',
		'reconnecting', 'unauthenticated', 'default', 'addressed',
		'configured', and 'suspended'; however not all USB Device
		Controllers support reporting all states.
Users:

What:		/sys/class/udc/<udc>/function
Date:		June 2017
KernelVersion:	4.13
Contact:	Felipe Balbi <<EMAIL>>
Description:
		Prints out name of currently running USB Gadget Driver.
Users:
