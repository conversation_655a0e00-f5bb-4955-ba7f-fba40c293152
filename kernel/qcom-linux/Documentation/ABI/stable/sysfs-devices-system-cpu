What: 		/sys/devices/system/cpu/dscr_default
Date:		13-May-2014
KernelVersion:	v3.15.0
Contact:
Description:	Writes are equivalent to writing to
		/sys/devices/system/cpu/cpuN/dscr on all CPUs.
		Reads return the last written value or 0.
		This value is not a global default: it is a way to set
		all per-CPU defaults at the same time.
Values:		64 bit unsigned integer (bit field)

What: 		/sys/devices/system/cpu/cpu[0-9]+/dscr
Date:		13-May-2014
KernelVersion:	v3.15.0
Contact:
Description:	Default value for the Data Stream Control Register (DSCR) on
		a CPU.
		This default value is used when the kernel is executing and
		for any process that has not set the DSCR itself.
		If a process ever sets the DSCR (via direct access to the
		SPR) that value will be persisted for that process and used
		on any CPU where it executes (overriding the value described
		here).
		If set by a process it will be inherited by child processes.
Values:		64 bit unsigned integer (bit field)
