What: 		/sys/firmware/acpi/pm_profile
Date:		03-Nov-2011
KernelVersion:	v3.2
Contact:	<EMAIL>
Description: 	The ACPI pm_profile sysfs interface exports the platform
		power management (and performance) requirement expectations
		as provided by BIOS. The integer value is directly passed as
		retrieved from the FADT ACPI table.
Values:         For possible values see ACPI specification:
		5.2.9 Fixed ACPI Description Table (FADT)
		Field: Preferred_PM_Profile

		Currently these values are defined by spec:
		0 Unspecified
		1 Desktop
		2 Mobile
		3 Workstation
		4 Enterprise Server
		5 SOHO Server
		6 Appliance PC
		7 Performance Server
		>7 Reserved
