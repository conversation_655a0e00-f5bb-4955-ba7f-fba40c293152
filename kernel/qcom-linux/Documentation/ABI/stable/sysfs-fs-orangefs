What:			/sys/fs/orangefs/perf_counters/*
Date:			Jun 2015
Contact:		<PERSON> <<EMAIL>>
Description:
			Counters and settings for various caches.
			Read only.


What:			/sys/fs/orangefs/perf_counter_reset
Date:			June 2015
Contact:		<PERSON> <<EMAIL>>
Description:
			echo a 0 or a 1 into perf_counter_reset to
			reset all the counters in
			/sys/fs/orangefs/perf_counters
			except ones with PINT_PERF_PRESERVE set.


What:			/sys/fs/orangefs/perf_time_interval_secs
Date:			Jun 2015
Contact:		<PERSON> <<EMAIL>>
Description:
			Length of perf counter intervals in
			seconds.


What:			/sys/fs/orangefs/perf_history_size
Date:			Jun 2015
Contact:		<PERSON> <<EMAIL>>
Description:
			The perf_counters cache statistics have N, or
			perf_history_size, samples. The default is
			one.

			Every perf_time_interval_secs the (first)
			samples are reset.

			If N is greater than one, the "current" set
			of samples is reset, and the samples from the
			other N-1 intervals remain available.


What:			/sys/fs/orangefs/op_timeout_secs
Date:			Jun 2015
Contact:		<PERSON> <<EMAIL>>
Description:
			Service operation timeout in seconds.


What:			/sys/fs/orangefs/slot_timeout_secs
Date:			Jun 2015
Contact:		<PERSON> <<EMAIL>>
Description:
			"Slot" timeout in seconds. A "slot"
			is an indexed buffer in the shared
			memory segment used for communication
			between the kernel module and userspace.
			Slots are requested and waited for,
			the wait times out after slot_timeout_secs.


What:			/sys/fs/orangefs/acache/*
Date:			Jun 2015
Contact:		Mike Marshall <<EMAIL>>
Description:
			Attribute cache configurable settings.


What:			/sys/fs/orangefs/ncache/*
Date:			Jun 2015
Contact:		Mike Marshall <<EMAIL>>
Description:
			Name cache configurable settings.


What:			/sys/fs/orangefs/capcache/*
Date:			Jun 2015
Contact:		Mike Marshall <<EMAIL>>
Description:
			Capability cache configurable settings.


What:			/sys/fs/orangefs/ccache/*
Date:			Jun 2015
Contact:		Mike Marshall <<EMAIL>>
Description:
			Credential cache configurable settings.
