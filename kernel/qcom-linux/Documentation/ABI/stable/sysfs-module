What:		/sys/module
Description:
	The /sys/module tree consists of the following structure:

	/sys/module/MODULENAME
		The name of the module that is in the kernel.  This
		module name will always show up if the module is loaded as a
		dynamic module.  If it is built directly into the kernel, it
		will only show up if it has a version or at least one
		parameter.

		Note: The conditions of creation in the built-in case are not
		by design and may be removed in the future.

	/sys/module/MODULENAME/parameters
		This directory contains individual files that are each
		individual parameters of the module that are able to be
		changed at runtime.  See the individual module
		documentation as to the contents of these parameters and
		what they accomplish.

		Note: The individual parameter names and values are not
		considered stable, only the fact that they will be
		placed in this location within sysfs.  See the
		individual driver documentation for details as to the
		stability of the different parameters.

	/sys/module/MODULENAME/refcnt
		If the module is able to be unloaded from the kernel, this file
		will contain the current reference count of the module.

		Note: If the module is built into the kernel, or if the
		CONFIG_MODULE_UNLOAD kernel configuration value is not enabled,
		this file will not be present.
