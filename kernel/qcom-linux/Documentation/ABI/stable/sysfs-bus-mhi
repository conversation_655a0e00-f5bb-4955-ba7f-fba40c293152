What:		/sys/bus/mhi/devices/.../serialnumber
Date:		Sept 2020
KernelVersion:	5.10
Contact:	<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Description:	The file holds the serial number of the client device obtained
		using a BHI (Boot Host Interface) register read after at least
		one attempt to power up the device has been done. If read
		without having the device power on at least once, the file will
		read all 0's.
Users:		Any userspace application or clients interested in device info.

What:		/sys/bus/mhi/devices/.../oem_pk_hash
Date:		Sept 2020
KernelVersion:	5.10
Contact:	<PERSON><PERSON><PERSON><PERSON>t <<EMAIL>>
Description:	The file holds the OEM PK Hash value of the endpoint device
		obtained using a BHI (Boot Host Interface) register read after
		at least one attempt to power up the device has been done. If
		read without having the device power on at least once, the file
		will read all 0's.
Users:		Any userspace application or clients interested in device info.
