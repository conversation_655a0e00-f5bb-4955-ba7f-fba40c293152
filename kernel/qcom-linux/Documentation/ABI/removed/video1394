What:		video1394 (a.k.a. "OHCI-1394 Video support" for FireWire)
Date:		May 2010 (scheduled), finally removed in kernel v2.6.37
Contact:	<EMAIL>
Description:
	/dev/video1394/* were character device files, one for each FireWire
	controller, which were used for isochronous I/O.  It was added as an
	alternative to raw1394's isochronous I/O functionality which had
	performance issues in its first generation.  Any video1394 user had
	to use raw1394 + libraw1394 too because video1394 did not provide
	asynchronous I/O for device discovery and configuration.
	Replaced by /dev/fw*, i.e. the <linux/firewire-cdev.h> ABI of
	firewire-core.

Users:
	libdc1394 (works with firewire-cdev too, transparent to library ABI
	users)
