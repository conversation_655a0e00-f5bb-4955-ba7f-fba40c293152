What:		/sys/kernel/slab
Date:		May 2007
KernelVersion:	2.6.22
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>,
		<PERSON> <<EMAIL>>
Description:
		The /sys/kernel/slab directory contains a snapshot of the
		internal state of the SLUB allocator for each cache.  Certain
		files may be modified to change the behavior of the cache (and
		any cache it aliases, if any).
Users:		kernel memory tuning tools

What:		/sys/kernel/slab/cache/aliases
Date:		May 2007
KernelVersion:	2.6.22
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>,
		<PERSON> <<EMAIL>>
Description:
		The aliases file is read-only and specifies how many caches
		have merged into this cache.

What:		/sys/kernel/slab/cache/align
Date:		May 2007
KernelVersion:	2.6.22
Contact:	<PERSON><PERSON><PERSON> <pen<PERSON>@cs.helsinki.fi>,
		<PERSON> <<EMAIL>>
Description:
		The align file is read-only and specifies the cache's object
		alignment in bytes.

What:		/sys/kernel/slab/cache/alloc_calls
Date:		May 2007
KernelVersion:	2.6.22
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>,
		<PERSON> <<EMAIL>>
Description:
		The alloc_calls file is read-only and lists the kernel code
		locations from which allocations for this cache were performed.
		The alloc_calls file only contains information if debugging is
		enabled for that cache (see Documentation/vm/slub.rst).

What:		/sys/kernel/slab/cache/alloc_fastpath
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The alloc_fastpath file shows how many objects have been
		allocated using the fast path.  It can be written to clear the
		current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/alloc_from_partial
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The alloc_from_partial file shows how many times a cpu slab has
		been full and it has been refilled by using a slab from the list
		of partially used slabs.  It can be written to clear the current
		count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/alloc_refill
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The alloc_refill file shows how many times the per-cpu freelist
		was empty but there were objects available as the result of
		remote cpu frees.  It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/alloc_slab
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The alloc_slab file is shows how many times a new slab had to
		be allocated from the page allocator.  It can be written to
		clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/alloc_slowpath
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The alloc_slowpath file shows how many objects have been
		allocated using the slow path because of a refill or
		allocation from a partial or new slab.  It can be written to
		clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/cache_dma
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The cache_dma file is read-only and specifies whether objects
		are from ZONE_DMA.
		Available when CONFIG_ZONE_DMA is enabled.

What:		/sys/kernel/slab/cache/cpu_slabs
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The cpu_slabs file is read-only and displays how many cpu slabs
		are active and their NUMA locality.

What:		/sys/kernel/slab/cache/cpuslab_flush
Date:		April 2009
KernelVersion:	2.6.31
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The file cpuslab_flush shows how many times a cache's cpu slabs
		have been flushed as the result of destroying or shrinking a
		cache, a cpu going offline, or as the result of forcing an
		allocation from a certain node.  It can be written to clear the
		current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/ctor
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The ctor file is read-only and specifies the cache's object
		constructor function, which is invoked for each object when a
		new slab is allocated.

What:		/sys/kernel/slab/cache/deactivate_empty
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The deactivate_empty file shows how many times an empty cpu slab
		was deactivated.  It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/deactivate_full
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The deactivate_full file shows how many times a full cpu slab
		was deactivated.  It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/deactivate_remote_frees
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The deactivate_remote_frees file shows how many times a cpu slab
		has been deactivated and contained free objects that were freed
		remotely.  It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/deactivate_to_head
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The deactivate_to_head file shows how many times a partial cpu
		slab was deactivated and added to the head of its node's partial
		list.  It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/deactivate_to_tail
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The deactivate_to_tail file shows how many times a partial cpu
		slab was deactivated and added to the tail of its node's partial
		list.  It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/destroy_by_rcu
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The destroy_by_rcu file is read-only and specifies whether
		slabs (not objects) are freed by rcu.

What:		/sys/kernel/slab/cache/free_add_partial
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The free_add_partial file shows how many times an object has
		been freed in a full slab so that it had to added to its node's
		partial list.  It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/free_calls
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The free_calls file is read-only and lists the locations of
		object frees if slab debugging is enabled (see
		Documentation/vm/slub.rst).

What:		/sys/kernel/slab/cache/free_fastpath
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The free_fastpath file shows how many objects have been freed
		using the fast path because it was an object from the cpu slab.
		It can be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/free_frozen
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The free_frozen file shows how many objects have been freed to
		a frozen slab (i.e. a remote cpu slab).  It can be written to
		clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/free_remove_partial
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The free_remove_partial file shows how many times an object has
		been freed to a now-empty slab so that it had to be removed from
		its node's partial list.  It can be written to clear the current
		count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/free_slab
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The free_slab file shows how many times an empty slab has been
		freed back to the page allocator.  It can be written to clear
		the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/free_slowpath
Date:		February 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The free_slowpath file shows how many objects have been freed
		using the slow path (i.e. to a full or partial slab).  It can
		be written to clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/hwcache_align
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The hwcache_align file is read-only and specifies whether
		objects are aligned on cachelines.

What:		/sys/kernel/slab/cache/min_partial
Date:		February 2009
KernelVersion:	2.6.30
Contact:	Pekka Enberg <<EMAIL>>,
		David Rientjes <<EMAIL>>
Description:
		The min_partial file specifies how many empty slabs shall
		remain on a node's partial list to avoid the overhead of
		allocating new slabs.  Such slabs may be reclaimed by utilizing
		the shrink file.

What:		/sys/kernel/slab/cache/object_size
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The object_size file is read-only and specifies the cache's
		object size.

What:		/sys/kernel/slab/cache/objects
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The objects file is read-only and displays how many objects are
		active and from which nodes they are from.

What:		/sys/kernel/slab/cache/objects_partial
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The objects_partial file is read-only and displays how many
		objects are on partial slabs and from which nodes they are
		from.

What:		/sys/kernel/slab/cache/objs_per_slab
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The file objs_per_slab is read-only and specifies how many
		objects may be allocated from a single slab of the order
		specified in /sys/kernel/slab/cache/order.

What:		/sys/kernel/slab/cache/order
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The order file specifies the page order at which new slabs are
		allocated.  It is writable and can be changed to increase the
		number of objects per slab.  If a slab cannot be allocated
		because of fragmentation, SLUB will retry with the minimum order
		possible depending on its characteristics.
		When debug_guardpage_minorder=N (N > 0) parameter is specified
		(see Documentation/admin-guide/kernel-parameters.rst), the minimum possible
		order is used and this sysfs entry can not be used to change
		the order at run time.

What:		/sys/kernel/slab/cache/order_fallback
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The order_fallback file shows how many times an allocation of a
		new slab has not been possible at the cache's order and instead
		fallen back to its minimum possible order.  It can be written to
		clear the current count.
		Available when CONFIG_SLUB_STATS is enabled.

What:		/sys/kernel/slab/cache/partial
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The partial file is read-only and displays how long many
		partial slabs there are and how long each node's list is.

What:		/sys/kernel/slab/cache/poison
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The poison file specifies whether objects should be poisoned
		when a new slab is allocated.

What:		/sys/kernel/slab/cache/reclaim_account
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The reclaim_account file specifies whether the cache's objects
		are reclaimable (and grouped by their mobility).

What:		/sys/kernel/slab/cache/red_zone
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The red_zone file specifies whether the cache's objects are red
		zoned.

What:		/sys/kernel/slab/cache/remote_node_defrag_ratio
Date:		January 2008
KernelVersion:	2.6.25
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The file remote_node_defrag_ratio specifies the percentage of
		times SLUB will attempt to refill the cpu slab with a partial
		slab from a remote node as opposed to allocating a new slab on
		the local node.  This reduces the amount of wasted memory over
		the entire system but can be expensive.
		Available when CONFIG_NUMA is enabled.

What:		/sys/kernel/slab/cache/sanity_checks
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The sanity_checks file specifies whether expensive checks
		should be performed on free and, at minimum, enables double free
		checks.  Caches that enable sanity_checks cannot be merged with
		caches that do not.

What:		/sys/kernel/slab/cache/shrink
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The shrink file is used to reclaim unused slab cache
		memory from a cache.  Empty per-cpu or partial slabs
		are freed and the partial list is sorted so the slabs
		with the fewest available objects are used first.
		It only accepts a value of "1" on write for shrinking
		the cache. Other input values are considered invalid.
		Shrinking slab caches might be expensive and can
		adversely impact other running applications.  So it
		should be used with care.

What:		/sys/kernel/slab/cache/slab_size
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The slab_size file is read-only and specifies the object size
		with metadata (debugging information and alignment) in bytes.

What:		/sys/kernel/slab/cache/slabs
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The slabs file is read-only and displays how long many slabs
		there are (both cpu and partial) and from which nodes they are
		from.

What:		/sys/kernel/slab/cache/store_user
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The store_user file specifies whether the location of
		allocation or free should be tracked for a cache.

What:		/sys/kernel/slab/cache/total_objects
Date:		April 2008
KernelVersion:	2.6.26
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The total_objects file is read-only and displays how many total
		objects a cache has and from which nodes they are from.

What:		/sys/kernel/slab/cache/trace
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		The trace file specifies whether object allocations and frees
		should be traced.

What:		/sys/kernel/slab/cache/validate
Date:		May 2007
KernelVersion:	2.6.22
Contact:	Pekka Enberg <<EMAIL>>,
		Christoph Lameter <<EMAIL>>
Description:
		Writing to the validate file causes SLUB to traverse all of its
		cache's objects and check the validity of metadata.
