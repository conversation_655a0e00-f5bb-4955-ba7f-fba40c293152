What:		/sys/devices/platform/docg3/f[0-3]_dps[01]_is_keylocked
Date:		November 2011
KernelVersion:	3.3
Contact:	<PERSON> <<EMAIL>>
Description:
		Show whether the floor (0 to 4), protection area (0 or 1) is
		keylocked. Each docg3 chip (or floor) has 2 protection areas,
		which can cover any part of it, block aligned, called DPS.
		The protection has information embedded whether it blocks reads,
		writes or both.
		The result is:
		0 -> the DPS is not keylocked
		1 -> the DPS is keylocked
Users:		None identified so far.

What:		/sys/devices/platform/docg3/f[0-3]_dps[01]_protection_key
Date:		November 2011
KernelVersion:	3.3
Contact:	<PERSON> <<EMAIL>>
Description:
		Enter the protection key for the floor (0 to 4), protection area
		(0 or 1). Each docg3 chip (or floor) has 2 protection areas,
		which can cover any part of it, block aligned, called DPS.
		The protection has information embedded whether it blocks reads,
		writes or both.
		The protection key is a string of 8 bytes (value 0-255).
		Entering the correct value toggle the lock, and can be observed
		through f[0-3]_dps[01]_is_keylocked.
		Possible values are:
			- 8 bytes
		Typical values are:
			- "00000000"
			- "12345678"
Users:		None identified so far.
