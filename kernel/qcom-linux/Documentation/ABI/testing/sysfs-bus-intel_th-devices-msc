What:		/sys/bus/intel_th/devices/<intel_th_id>-msc<msc-id>/wrap
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	(RW) Configure MSC buffer wrapping. 1 == wrapping enabled.

What:		/sys/bus/intel_th/devices/<intel_th_id>-msc<msc-id>/mode
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	(RW) Configure MSC operating mode:
		  - "single", for contiguous buffer mode (high-order alloc);
		  - "multi", for multiblock mode;
		  - "ExI", for DCI handler mode;
		  - "debug", for debug mode;
		  - any of the currently loaded buffer sinks.
		If operating mode changes, existing buffer is deallocated,
		provided there are no active users and tracing is not enabled,
		otherwise the write will fail.

What:		/sys/bus/intel_th/devices/<intel_th_id>-msc<msc-id>/nr_pages
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	(RW) Configure MSC buffer size for "single" or "multi" modes.
		In single mode, this is a single number of pages, has to be
		power of 2. In multiblock mode, this is a comma-separated list
		of numbers of pages for each window to be allocated. Number of
		windows is not limited.
		Writing to this file deallocates existing buffer (provided
		there are no active users and tracing is not enabled) and then
		allocates a new one.

What:		/sys/bus/intel_th/devices/<intel_th_id>-msc<msc-id>/win_switch
Date:		May 2019
KernelVersion:	5.2
Contact:	Alexander Shishkin <<EMAIL>>
Description:	(RW) Trigger window switch for the MSC's buffer, in
		multi-window mode. In "multi" mode, accepts writes of "1", thereby
		triggering a window switch for the buffer. Returns an error in any
		other operating mode or attempts to write something other than "1".

