What:		/sys/class/net/<iface>/cdc_ncm/min_tx_pkt
Date:		May 2014
KernelVersion:	3.16
Contact:	<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The driver will pad NCM Transfer Blocks (NTBs) longer
		than this to tx_max, allowing the device to receive
		tx_max sized frames with no terminating short
		packet. NTBs shorter than this limit are transmitted
		as-is, without any padding, and are terminated with a
		short USB packet.

		Padding to tx_max allows the driver to transmit NTBs
		back-to-back without any interleaving short USB
		packets.  This reduces the number of short packet
		interrupts in the device, and represents a tradeoff
		between USB bus bandwidth and device DMA optimization.

		Set to 0 to pad all frames. Set greater than tx_max to
		disable all padding.

What:		/sys/class/net/<iface>/cdc_ncm/ndp_to_end
Date:		Dec 2015
KernelVersion:	4.5
Contact:	<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Boolean attribute showing the status of the "NDP to
		end" quirk.  Defaults to 'N', except for devices
		already known to need it enabled.

		The "NDP to end" quirk makes the driver place the NDP
		(the packet index table) after the payload.  The NCM
		specification does not mandate this, but some devices
		are known to be more restrictive. Write 'Y' to this
		attribute for temporary testing of a suspect device
		failing to work with the default driver settings.

		A device entry should be added to the driver if this
		quirk is found to be required.

What:		/sys/class/net/<iface>/cdc_ncm/rx_max
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		The maximum NTB size for RX.  Cannot exceed the
		maximum value supported by the device. Must allow at
		least one max sized datagram plus headers.

		The actual limits are device dependent.  See
		dwNtbInMaxSize.

		Note: Some devices will silently ignore changes to
		this value, resulting in oversized NTBs and
		corresponding framing errors.

What:		/sys/class/net/<iface>/cdc_ncm/tx_max
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		The maximum NTB size for TX.  Cannot exceed the
		maximum value supported by the device.  Must allow at
		least one max sized datagram plus headers.

		The actual limits are device dependent.  See
		dwNtbOutMaxSize.

What:		/sys/class/net/<iface>/cdc_ncm/tx_timer_usecs
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		Datagram aggregation timeout in µs. The driver will
		wait up to 3 times this timeout for more datagrams to
		aggregate before transmitting an NTB frame.

		Valid range: 5 to 4000000

		Set to 0 to disable aggregation.

The following read-only attributes all represent fields of the
structure defined in section 6.2.1 "GetNtbParameters" of "Universal
Serial Bus Communications Class Subclass Specifications for Network
Control Model Devices" (CDC NCM), Revision 1.0 (Errata 1), November
24, 2010 from USB Implementers Forum, Inc.  The descriptions are
quoted from table 6-3 of CDC NCM: "NTB Parameter Structure".

What:		/sys/class/net/<iface>/cdc_ncm/bmNtbFormatsSupported
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		Bit 0: 16-bit NTB supported (set to 1)
		Bit 1: 32-bit NTB supported
		Bits 2 – 15: reserved (reset to zero; must be ignored by host)

What:		/sys/class/net/<iface>/cdc_ncm/dwNtbInMaxSize
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		IN NTB Maximum Size in bytes

What:		/sys/class/net/<iface>/cdc_ncm/wNdpInDivisor
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		Divisor used for IN NTB Datagram payload alignment

What:		/sys/class/net/<iface>/cdc_ncm/wNdpInPayloadRemainder
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		Remainder used to align input datagram payload within
		the NTB: (Payload Offset) mod (wNdpInDivisor) =
		wNdpInPayloadRemainder

What:		/sys/class/net/<iface>/cdc_ncm/wNdpInAlignment
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		NDP alignment modulus for NTBs on the IN pipe. Shall
		be a power of 2, and shall be at least 4.

What:		/sys/class/net/<iface>/cdc_ncm/dwNtbOutMaxSize
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		OUT NTB Maximum Size

What:		/sys/class/net/<iface>/cdc_ncm/wNdpOutDivisor
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		OUT NTB Datagram alignment modulus

What:		/sys/class/net/<iface>/cdc_ncm/wNdpOutPayloadRemainder
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		Remainder used to align output datagram payload
		offsets within the NTB: Padding, shall be transmitted
		as zero by function, and ignored by host.  (Payload
		Offset) mod (wNdpOutDivisor) = wNdpOutPayloadRemainder

What:		/sys/class/net/<iface>/cdc_ncm/wNdpOutAlignment
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		NDP alignment modulus for use in NTBs on the OUT
		pipe. Shall be a power of 2, and shall be at least 4.

What:		/sys/class/net/<iface>/cdc_ncm/wNtbOutMaxDatagrams
Date:		May 2014
KernelVersion:	3.16
Contact:	Bjørn Mork <<EMAIL>>
Description:
		Maximum number of datagrams that the host may pack
		into a single OUT NTB. Zero means that the device
		imposes no limit.
