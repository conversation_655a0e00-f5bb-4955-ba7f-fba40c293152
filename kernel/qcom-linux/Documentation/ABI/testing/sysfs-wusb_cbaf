What:           /sys/bus/usb/drivers/wusb_cbaf/.../wusb_*
Date:           August 2008
KernelVersion:  2.6.27
Contact:        <PERSON> <<EMAIL>>
Description:
                Various files for managing Cable Based Association of
                (wireless) USB devices.

                The sequence of operations should be:

                1. Device is plugged in.

                2. The connection manager (CM) sees a device with CBA capability.
                   (the wusb_chid etc. files in /sys/devices/blah/OURDEVICE).

                3. The CM writes the host name, supported band groups,
                   and the CHID (host ID) into the wusb_host_name,
                   wusb_host_band_groups and wusb_chid files. These
                   get sent to the device and the CDID (if any) for
                   this host is requested.

                4. The CM can verify that the device's supported band
                   groups (wusb_device_band_groups) are compatible
                   with the host.

                5. The CM reads the wusb_cdid file.

                6. The CM looks it up its database.

                   - If it has a matching CHID,CDID entry, the device
                     has been authorized before and nothing further
                     needs to be done.

                   - If the CDID is zero (or the CM doesn't find a
                     matching CDID in its database), the device is
                     assumed to be not known.  The CM may associate
                     the host with device by: writing a randomly
                     generated CDID to wusb_cdid and then a random CK
                     to wusb_ck (this uploads the new CC to the
                     device).

                     CMD may choose to prompt the user before
                     associating with a new device.

                7. Device is unplugged.

                References:
                  [WUSB-AM] Association Models Supplement to the
                            Certified Wireless Universal Serial Bus
                            Specification, version 1.0.

What:           /sys/bus/usb/drivers/wusb_cbaf/.../wusb_chid
Date:           August 2008
KernelVersion:  2.6.27
Contact:        David Vrabel <<EMAIL>>
Description:
                The CHID of the host formatted as 16 space-separated
                hex octets.

                Writes fetches device's supported band groups and the
                the CDID for any existing association with this host.

What:           /sys/bus/usb/drivers/wusb_cbaf/.../wusb_host_name
Date:           August 2008
KernelVersion:  2.6.27
Contact:        David Vrabel <<EMAIL>>
Description:
                A friendly name for the host as a UTF-8 encoded string.

What:           /sys/bus/usb/drivers/wusb_cbaf/.../wusb_host_band_groups
Date:           August 2008
KernelVersion:  2.6.27
Contact:        David Vrabel <<EMAIL>>
Description:
                The band groups supported by the host, in the format
                defined in [WUSB-AM].

What:           /sys/bus/usb/drivers/wusb_cbaf/.../wusb_device_band_groups
Date:           August 2008
KernelVersion:  2.6.27
Contact:        David Vrabel <<EMAIL>>
Description:
                The band groups supported by the device, in the format
                defined in [WUSB-AM].

What:           /sys/bus/usb/drivers/wusb_cbaf/.../wusb_cdid
Date:           August 2008
KernelVersion:  2.6.27
Contact:        David Vrabel <<EMAIL>>
Description:
                The device's CDID formatted as 16 space-separated hex
                octets.

What:           /sys/bus/usb/drivers/wusb_cbaf/.../wusb_ck
Date:           August 2008
KernelVersion:  2.6.27
Contact:        David Vrabel <<EMAIL>>
Description:
                Write 16 space-separated random, hex octets to
                associate with the device.
