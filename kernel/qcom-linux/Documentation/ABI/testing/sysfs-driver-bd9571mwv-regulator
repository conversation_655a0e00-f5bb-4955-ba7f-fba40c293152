What:		/sys/bus/i2c/devices/.../bd9571mwv-regulator.*.auto/backup_mode
Date:		Jul 2018
KernelVersion:	4.19
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:	Read/write the current state of DDR Backup Mode, which controls
		if DDR power rails will be kept powered during system suspend.
		("on"/"1" = enabled, "off"/"0" = disabled).
		Two types of power switches (or control signals) can be used:
		  A. With a momentary power switch (or pulse signal), DDR
		     Backup Mode is enabled by default when available, as the
		     PMIC will be configured only during system suspend.
		  B. With a toggle power switch (or level signal), the
		     following steps must be followed exactly:
		       1. Configure PMIC for backup mode, to change the role of
			  the accessory power switch from a power switch to a
			  wake-up switch,
		       2. Switch accessory power switch off, to prepare for
			  system suspend, which is a manual step not controlled
			  by software,
		       3. Suspend system,
		       4. Switch accessory power switch on, to resume the
			  system.
		     DDR Backup Mode must be explicitly enabled by the user,
		     to invoke step 1.
		See also Documentation/devicetree/bindings/mfd/bd9571mwv.txt.
Users:		User space applications for embedded boards equipped with a
		BD9571MWV PMIC.
