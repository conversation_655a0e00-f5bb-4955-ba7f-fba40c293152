What:		/sys/firmware/qemu_fw_cfg/
Date:		August 2015
Contact:	<PERSON> <<EMAIL>>
Description:
		Several different architectures supported by QEMU (x86, arm,
		sun4*, ppc/mac) are provisioned with a firmware configuration
		(fw_cfg) device, originally intended as a way for the host to
		provide configuration data to the guest firmware. Starting
		with QEMU v2.4, arbitrary fw_cfg file entries may be specified
		by the user on the command line, which makes fw_cfg additionally
		useful as an out-of-band, asynchronous mechanism for providing
		configuration data to the guest userspace.

		The authoritative guest-side hardware interface documentation
		to the fw_cfg device can be found in "docs/specs/fw_cfg.txt"
		in the QEMU source tree.

		=== SysFS fw_cfg Interface ===

		The fw_cfg sysfs interface described in this document is only
		intended to display discoverable blobs (i.e., those registered
		with the file directory), as there is no way to determine the
		presence or size of "legacy" blobs (with selector keys between
		0x0002 and 0x0018) programmatically.

		All fw_cfg information is shown under:

			/sys/firmware/qemu_fw_cfg/

		The only legacy blob displayed is the fw_cfg device revision:

			/sys/firmware/qemu_fw_cfg/rev

		--- Discoverable fw_cfg blobs by selector key ---

		All discoverable blobs listed in the fw_cfg file directory are
		displayed as entries named after their unique selector key
		value, e.g.:

			/sys/firmware/qemu_fw_cfg/by_key/32
			/sys/firmware/qemu_fw_cfg/by_key/33
			/sys/firmware/qemu_fw_cfg/by_key/34
			...

		Each such fw_cfg sysfs entry has the following values exported
		as attributes:

		name  	: The 56-byte nul-terminated ASCII string used as the
			  blob's 'file name' in the fw_cfg directory.
		size  	: The length of the blob, as given in the fw_cfg
			  directory.
		key	: The value of the blob's selector key as given in the
			  fw_cfg directory. This value is the same as used in
			  the parent directory name.
		raw	: The raw bytes of the blob, obtained by selecting the
			  entry via the control register, and reading a number
			  of bytes equal to the blob size from the data
			  register.

		--- Listing fw_cfg blobs by file name ---

		While the fw_cfg device does not impose any specific naming
		convention on the blobs registered in the file directory,
		QEMU developers have traditionally used path name semantics
		to give each blob a descriptive name. For example:

			"bootorder"
			"genroms/kvmvapic.bin"
			"etc/e820"
			"etc/boot-fail-wait"
			"etc/system-states"
			"etc/table-loader"
			"etc/acpi/rsdp"
			"etc/acpi/tables"
			"etc/smbios/smbios-tables"
			"etc/smbios/smbios-anchor"
			...

		In addition to the listing by unique selector key described
		above, the fw_cfg sysfs driver also attempts to build a tree
		of directories matching the path name components of fw_cfg
		blob names, ending in symlinks to the by_key entry for each
		"basename", as illustrated below (assume current directory is
		/sys/firmware):

		    qemu_fw_cfg/by_name/bootorder -> ../by_key/38
		    qemu_fw_cfg/by_name/etc/e820 -> ../../by_key/35
		    qemu_fw_cfg/by_name/etc/acpi/rsdp -> ../../../by_key/41
		    ...

		Construction of the directory tree and symlinks is done on a
		"best-effort" basis, as there is no guarantee that components
		of fw_cfg blob names are always "well behaved". I.e., there is
		the possibility that a symlink (basename) will conflict with
		a dirname component of another fw_cfg blob, in which case the
		creation of the offending /sys/firmware/qemu_fw_cfg/by_name
		entry will be skipped.

		The authoritative list of entries will continue to be found
		under the /sys/firmware/qemu_fw_cfg/by_key directory.
