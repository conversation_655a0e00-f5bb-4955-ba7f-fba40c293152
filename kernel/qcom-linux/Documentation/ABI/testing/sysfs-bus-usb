What:		/sys/bus/usb/devices/INTERFACE/authorized
Date:		August 2015
Description:
		This allows to authorize (1) or deauthorize (0)
		individual interfaces instead a whole device
		in contrast to the device authorization.
		If a deauthorized interface will be authorized
		so the driver probing must be triggered manually
		by writing INTERFACE to /sys/bus/usb/drivers_probe
		This allows to avoid side-effects with drivers
		that need multiple interfaces.
		A deauthorized interface cannot be probed or claimed.

What:		/sys/bus/usb/devices/usbX/interface_authorized_default
Date:		August 2015
Description:
		This is used as value that determines if interfaces
		would be authorized by default.
		The value can be 1 or 0. It's by default 1.

What:		/sys/bus/usb/device/.../authorized
Date:		July 2008
KernelVersion:	2.6.26
Contact:	<PERSON> <<EMAIL>>
Description:
		Authorized devices are available for use by device
		drivers, non-authorized one are not.  By default, wired
		USB devices are authorized.

		Certified Wireless USB devices are not authorized
		initially and should be (by writing 1) after the
		device has been authenticated.

What:		/sys/bus/usb/device/.../wusb_cdid
Date:		July 2008
KernelVersion:	2.6.27
Contact:	<PERSON> <<EMAIL>>
Description:
		For Certified Wireless USB devices only.

		A devices's CDID, as 16 space-separated hex octets.

What:		/sys/bus/usb/device/.../wusb_ck
Date:		July 2008
KernelVersion:	2.6.27
Contact:	David Vrabel <<EMAIL>>
Description:
		For Certified Wireless USB devices only.

		Write the device's connection key (CK) to start the
		authentication of the device.  The CK is 16
		space-separated hex octets.

What:		/sys/bus/usb/device/.../wusb_disconnect
Date:		July 2008
KernelVersion:	2.6.27
Contact:	David Vrabel <<EMAIL>>
Description:
		For Certified Wireless USB devices only.

		Write a 1 to force the device to disconnect
		(equivalent to unplugging a wired USB device).

What:		/sys/bus/usb/drivers/.../new_id
Date:		October 2011
Contact:	<EMAIL>
Description:
		Writing a device ID to this file will attempt to
		dynamically add a new device ID to a USB device driver.
		This may allow the driver to support more hardware than
		was included in the driver's static device ID support
		table at compile time. The format for the device ID is:
		idVendor idProduct bInterfaceClass RefIdVendor RefIdProduct
		The vendor ID and device ID fields are required, the
		rest is optional. The Ref* tuple can be used to tell the
		driver to use the same driver_data for the new device as
		it is used for the reference device.
		Upon successfully adding an ID, the driver will probe
		for the device and attempt to bind to it.  For example:
		# echo "8086 10f5" > /sys/bus/usb/drivers/foo/new_id

		Here add a new device (0458:7045) using driver_data from
		an already supported device (0458:704c):
		# echo "0458 7045 0 0458 704c" > /sys/bus/usb/drivers/foo/new_id

		Reading from this file will list all dynamically added
		device IDs in the same format, with one entry per
		line. For example:
		# cat /sys/bus/usb/drivers/foo/new_id
		8086 10f5
		dead beef 06
		f00d cafe

		The list will be truncated at PAGE_SIZE bytes due to
		sysfs restrictions.

What:		/sys/bus/usb-serial/drivers/.../new_id
Date:		October 2011
Contact:	<EMAIL>
Description:
		For serial USB drivers, this attribute appears under the
		extra bus folder "usb-serial" in sysfs; apart from that
		difference, all descriptions from the entry
		"/sys/bus/usb/drivers/.../new_id" apply.

What:		/sys/bus/usb/drivers/.../remove_id
Date:		November 2009
Contact:	CHENG Renquan <<EMAIL>>
Description:
		Writing a device ID to this file will remove an ID
		that was dynamically added via the new_id sysfs entry.
		The format for the device ID is:
		idVendor idProduct.	After successfully
		removing an ID, the driver will no longer support the
		device.  This is useful to ensure auto probing won't
		match the driver to the device.  For example:
		# echo "046d c315" > /sys/bus/usb/drivers/foo/remove_id

		Reading from this file will list the dynamically added
		device IDs, exactly like reading from the entry
		"/sys/bus/usb/drivers/.../new_id"

What:		/sys/bus/usb/devices/.../power/usb2_hardware_lpm
Date:		September 2011
Contact:	Andiry Xu <<EMAIL>>
Description:
		If CONFIG_PM is set and a USB 2.0 lpm-capable device is plugged
		in to a xHCI host which support link PM, it will perform a LPM
		test; if the test is passed and host supports USB2 hardware LPM
		(xHCI 1.0 feature), USB2 hardware LPM will be enabled for the
		device and the USB device directory will contain a file named
		power/usb2_hardware_lpm.  The file holds a string value (enable
		or disable) indicating whether or not USB2 hardware LPM is
		enabled for the device. Developer can write y/Y/1 or n/N/0 to
		the file to enable/disable the feature.

What:		/sys/bus/usb/devices/.../power/usb3_hardware_lpm_u1
		/sys/bus/usb/devices/.../power/usb3_hardware_lpm_u2
Date:		November 2015
Contact:	Kevin Strasser <<EMAIL>>
		Lu Baolu <<EMAIL>>
Description:
		If CONFIG_PM is set and a USB 3.0 lpm-capable device is plugged
		in to a xHCI host which supports link PM, it will check if U1
		and U2 exit latencies have been set in the BOS descriptor; if
		the check is passed and the host supports USB3 hardware LPM,
		USB3 hardware LPM will be enabled for the device and the USB
		device directory will contain two files named
		power/usb3_hardware_lpm_u1 and power/usb3_hardware_lpm_u2. These
		files hold a string value (enable or disable) indicating whether
		or not USB3 hardware LPM U1 or U2 is enabled for the device.

What:		/sys/bus/usb/devices/.../removable
Date:		February 2012
Contact:	Matthew Garrett <<EMAIL>>
Description:
		Some information about whether a given USB device is
		physically fixed to the platform can be inferred from a
		combination of hub descriptor bits and platform-specific data
		such as ACPI. This file will read either "removable" or
		"fixed" if the information is available, and "unknown"
		otherwise.

What:		/sys/bus/usb/devices/.../ltm_capable
Date:		July 2012
Contact:	Sarah Sharp <<EMAIL>>
Description:
		USB 3.0 devices may optionally support Latency Tolerance
		Messaging (LTM).  They indicate their support by setting a bit
		in the bmAttributes field of their SuperSpeed BOS descriptors.
		If that bit is set for the device, ltm_capable will read "yes".
		If the device doesn't support LTM, the file will read "no".
		The file will be present for all speeds of USB devices, and will
		always read "no" for USB 1.1 and USB 2.0 devices.

What:		/sys/bus/usb/devices/.../(hub interface)/portX
Date:		August 2012
Contact:	Lan Tianyu <<EMAIL>>
Description:
		The /sys/bus/usb/devices/.../(hub interface)/portX
		is usb port device's sysfs directory.

What:		/sys/bus/usb/devices/.../(hub interface)/portX/connect_type
Date:		January 2013
Contact:	Lan Tianyu <<EMAIL>>
Description:
		Some platforms provide usb port connect types through ACPI.
		This attribute is to expose these information to user space.
		The file will read "hotplug", "hardwired" and "not used" if the
		information is available, and "unknown" otherwise.

What:		/sys/bus/usb/devices/.../(hub interface)/portX/location
Date:		October 2018
Contact:	Bjørn Mork <<EMAIL>>
Description:
		Some platforms provide usb port physical location through
		firmware. This is used by the kernel to pair up logical ports
		mapping to the same physical connector. The attribute exposes the
		raw location value as a hex integer.


What:		/sys/bus/usb/devices/.../(hub interface)/portX/quirks
Date:		May 2018
Contact:	Nicolas Boichat <<EMAIL>>
Description:
		In some cases, we care about time-to-active for devices
		connected on a specific port (e.g. non-standard USB port like
		pogo pins), where the device to be connected is known in
		advance, and behaves well according to the specification.
		This attribute is a bit-field that controls the behavior of
		a specific port:
		 - Bit 0 of this field selects the "old" enumeration scheme,
		   as it is considerably faster (it only causes one USB reset
		   instead of 2).
		   The old enumeration scheme can also be selected globally
		   using /sys/module/usbcore/parameters/old_scheme_first, but
		   it is often not desirable as the new scheme was introduced to
		   increase compatibility with more devices.
		 - Bit 1 reduces TRSTRCY to the 10 ms that are required by the
		   USB 2.0 specification, instead of the 50 ms that are normally
		   used to help make enumeration work better on some high speed
		   devices.

What:		/sys/bus/usb/devices/.../(hub interface)/portX/over_current_count
Date:		February 2018
Contact:	Richard Leitner <<EMAIL>>
Description:
		Most hubs are able to detect over-current situations on their
		ports and report them to the kernel. This attribute is to expose
		the number of over-current situation occurred on a specific port
		to user space. This file will contain an unsigned 32 bit value
		which wraps to 0 after its maximum is reached. This file supports
		poll() for monitoring changes to this value in user space.

		Any time this value changes the corresponding hub device will send a
		udev event with the following attributes:

		OVER_CURRENT_PORT=/sys/bus/usb/devices/.../(hub interface)/portX
		OVER_CURRENT_COUNT=[current value of this sysfs attribute]

What:		/sys/bus/usb/devices/.../(hub interface)/portX/usb3_lpm_permit
Date:		November 2015
Contact:	Lu Baolu <<EMAIL>>
Description:
		Some USB3.0 devices are not friendly to USB3 LPM.  usb3_lpm_permit
		attribute allows enabling/disabling usb3 lpm of a port. It takes
		effect both before and after a usb device is enumerated. Supported
		values are "0" if both u1 and u2 are NOT permitted, "u1" if only u1
		is permitted, "u2" if only u2 is permitted, "u1_u2" if both u1 and
		u2 are permitted.

What:		/sys/bus/usb/devices/.../power/usb2_lpm_l1_timeout
Date:		May 2013
Contact:	Mathias Nyman <<EMAIL>>
Description:
		USB 2.0 devices may support hardware link power management (LPM)
		L1 sleep state. The usb2_lpm_l1_timeout attribute allows
		tuning the timeout for L1 inactivity timer (LPM timer), e.g.
		needed inactivity time before host requests the device to go to L1 sleep.
		Useful for power management tuning.
		Supported values are 0 - 65535 microseconds.

What:		/sys/bus/usb/devices/.../power/usb2_lpm_besl
Date:		May 2013
Contact:	Mathias Nyman <<EMAIL>>
Description:
		USB 2.0 devices that support hardware link power management (LPM)
		L1 sleep state now use a best effort service latency value (BESL) to
		indicate the best effort to resumption of service to the device after the
		initiation of the resume event.
		If the device does not have a preferred besl value then the host can select
		one instead. This usb2_lpm_besl attribute allows to tune the host selected besl
		value in order to tune power saving and service latency.

		Supported values are 0 - 15.
		More information on how besl values map to microseconds can be found in
		USB 2.0 ECN Errata for Link Power Management, section 4.10)

What:		/sys/bus/usb/devices/.../rx_lanes
Date:		March 2018
Contact:	Mathias Nyman <<EMAIL>>
Description:
		Number of rx lanes the device is using.
		USB 3.2 adds Dual-lane support, 2 rx and 2 tx lanes over Type-C.
		Inter-Chip SSIC devices support asymmetric lanes up to 4 lanes per
		direction. Devices before USB 3.2 are single lane (rx_lanes = 1)

What:		/sys/bus/usb/devices/.../tx_lanes
Date:		March 2018
Contact:	Mathias Nyman <<EMAIL>>
Description:
		Number of tx lanes the device is using.
		USB 3.2 adds Dual-lane support, 2 rx and 2 tx -lanes over Type-C.
		Inter-Chip SSIC devices support asymmetric lanes up to 4 lanes per
		direction. Devices before USB 3.2 are single lane (tx_lanes = 1)
