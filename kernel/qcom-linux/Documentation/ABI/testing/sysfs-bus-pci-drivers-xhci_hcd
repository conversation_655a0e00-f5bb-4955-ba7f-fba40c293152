What:		/sys/bus/pci/drivers/xhci_hcd/.../dbc
Date:		June 2017
Contact:	<PERSON> <<EMAIL>>
Description:
		xHCI compatible USB host controllers (i.e. super-speed
		USB3 controllers) are often implemented with the Debug
		Capability (DbC). It can present a debug device which
		is fully compliant with the USB framework and provides
		the equivalent of a very high performance full-duplex
		serial link for debug purpose.

		The DbC debug device shares a root port with xHCI host.
		When the DbC is enabled, the root port will be assigned
		to the Debug Capability. Otherwise, it will be assigned
		to xHCI.

		Writing "enable" to this attribute will enable the DbC
		functionality and the shared root port will be assigned
		to the DbC device. Writing "disable" to this attribute
		will disable the DbC functionality and the shared root
		port will roll back to the xHCI.

		Reading this attribute gives the state of the DbC. It
		can be one of the following states: disabled, enabled,
		initialized, connected, configured and stalled.
