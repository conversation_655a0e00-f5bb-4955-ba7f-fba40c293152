What:           /sys/module/xen_blkfront/parameters/max
Date:           June 2013
KernelVersion:  3.11
Contact:        <PERSON> <<EMAIL>>
Description:
                Maximum number of segments that the frontend will negotiate
                with the backend for indirect descriptors. The default value
                is 32 - higher value means more potential throughput but more
                memory usage. The backend picks the minimum of the frontend
                and its default backend value.
