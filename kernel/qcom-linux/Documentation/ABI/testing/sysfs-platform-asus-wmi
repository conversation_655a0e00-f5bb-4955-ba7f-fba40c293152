What:		/sys/devices/platform/<platform>/cpufv
Date:		Oct 2010
KernelVersion:	2.6.37
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Change CPU clock configuration (write-only).
		There are three available clock configuration:
		    * 0 -> Super Performance Mode
		    * 1 -> High Performance Mode
		    * 2 -> Power Saving Mode

What:		/sys/devices/platform/<platform>/camera
Date:		Jan 2010
KernelVersion:	2.6.39
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the camera. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/cardr
Date:		Jan 2010
KernelVersion:	2.6.39
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the card reader. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/touchpad
Date:		Jan 2010
KernelVersion:	2.6.39
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the card touchpad. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/lid_resume
Date:		May 2012
KernelVersion:	3.5
Contact:	"<PERSON><PERSON><PERSON>" <<EMAIL>>
Description:
		Resume on lid open. 1 means on, 0 means off.

What:		/sys/devices/platform/<platform>/fan_boost_mode
Date:		Sep 2019
KernelVersion:	5.3
Contact:	"Yurii Pavlovskyi" <<EMAIL>>
Description:
		Fan boost mode:
			* 0 - normal,
			* 1 - overboost,
			* 2 - silent
