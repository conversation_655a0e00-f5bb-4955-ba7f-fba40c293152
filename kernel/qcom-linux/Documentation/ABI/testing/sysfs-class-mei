What:		/sys/class/mei/
Date:		May 2014
KernelVersion:	3.17
Contact:	<PERSON> <<EMAIL>>
Description:
		The mei/ class sub-directory belongs to mei device class


What:		/sys/class/mei/meiN/
Date:		May 2014
KernelVersion:	3.17
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/class/mei/meiN directory is created for
		each probed mei device

What:		/sys/class/mei/meiN/fw_status
Date:		Nov 2014
KernelVersion:	3.19
Contact:	<PERSON> <<EMAIL>>
Description:	Display fw status registers content

		The ME FW writes its status information into fw status
		registers for BIOS and OS to monitor fw health.

		The register contains running state, power management
		state, error codes, and others. The way the registers
		are decoded depends on PCH or SoC generation.
		Also number of registers varies between 1 and 6
		depending on generation.

What:		/sys/class/mei/meiN/hbm_ver
Date:		Aug 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:	Display the negotiated HBM protocol version.

		The HBM protocol version negotiated
		between the driver and the device.

What:		/sys/class/mei/meiN/hbm_ver_drv
Date:		Aug 2016
KernelVersion:	4.9
Contact:	<PERSON> <<EMAIL>>
Description:	Display the driver HBM protocol version.

		The HBM protocol version supported by the driver.

What:		/sys/class/mei/meiN/tx_queue_limit
Date:		Jan 2018
KernelVersion:	4.16
Contact:	Tomas Winkler <<EMAIL>>
Description:	Configure tx queue limit

		Set maximal number of pending writes
		per opened session.

What:		/sys/class/mei/meiN/fw_ver
Date:		May 2018
KernelVersion:	4.18
Contact:	Tomas Winkler <<EMAIL>>
Description:	Display the ME firmware version.

		The version of the platform ME firmware is in format:
		<platform>:<major>.<minor>.<milestone>.<build_no>.
		There can be up to three such blocks for different
		FW components.

What:		/sys/class/mei/meiN/dev_state
Date:		Mar 2019
KernelVersion:	5.1
Contact:	Tomas Winkler <<EMAIL>>
Description:	Display the ME device state.

		The device state can have following values:
		INITIALIZING
		INIT_CLIENTS
		ENABLED
		RESETTING
		DISABLED
		POWER_DOWN
		POWER_UP
