What:		/sys/class/ocxl/<afu name>/afu_version
Date:		January 2018
Contact:	<EMAIL>
Description:	read only
		Version of the AFU, in the format <major>:<minor>
		Reflects what is read in the configuration space of the AFU

What:		/sys/class/ocxl/<afu name>/contexts
Date:		January 2018
Contact:	<EMAIL>
Description:	read only
		Number of contexts for the AFU, in the format <n>/<max>
		where:
			n:	number of currently active contexts, for debug
			max:	maximum number of contexts supported by the AFU

What:		/sys/class/ocxl/<afu name>/pp_mmio_size
Date:		January 2018
Contact:	<EMAIL>
Description:	read only
		Size of the per-process mmio area, as defined in the
		configuration space of the AFU

What:		/sys/class/ocxl/<afu name>/global_mmio_size
Date:		January 2018
Contact:	<EMAIL>
Description:	read only
		Size of the global mmio area, as defined in the
		configuration space of the AFU

What:		/sys/class/ocxl/<afu name>/global_mmio_area
Date:		January 2018
Contact:	<EMAIL>
Description:	read/write
		Give access the global mmio area for the AFU
