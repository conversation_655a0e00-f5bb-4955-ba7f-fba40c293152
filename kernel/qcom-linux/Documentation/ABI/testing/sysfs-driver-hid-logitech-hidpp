What:		/sys/bus/hid/drivers/logitech-hidpp-device/<dev>/range
Date:		Jan, 2016
KernelVersion:	4.6
Contact:	<EMAIL>
Description:
		(RW) This attribute controls the amount of 'turn' permitted in
		Logitech G920 wheel. Reading from the file shows the current
		range of the steering wheel. Writing a value within the min and
		max boundary sets the range of the wheel.

What:		/sys/bus/hid/drivers/logitech-hidpp-device/<dev>/builtin_power_supply
Date:		Apr, 2017
KernelVersion:	4.12
Contact:	<EMAIL>
Description:
		Presence of this file indicates that HID++ driver is capable of
		handling battery properties in the kernel. This way, upower can
		add a udev rule to decide whether or not it should use the
		internal unifying support or the generic kernel one.
