What:		/sys/devices/socX
Date:		January 2012
contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/devices/ directory contains a sub-directory for each
		System-on-Chip (SoC) device on a running platform. Information
		regarding each SoC can be obtained by reading sysfs files. This
		functionality is only available if implemented by the platform.

		The directory created for each SoC will also house information
		about devices which are commonly contained in /sys/devices/platform.
		It has been agreed that if an SoC device exists, its supported
		devices would be better suited to appear as children of that SoC.

What:		/sys/devices/socX/machine
Date:		January 2012
contact:	<PERSON> <<EMAIL>>
Description:
		Read-only attribute common to all SoCs. Contains the SoC machine
		name (e.g. Ux500).

What:		/sys/devices/socX/family
Date:		January 2012
contact:	<PERSON> <<EMAIL>>
Description:
		Read-only attribute common to all SoCs. Contains SoC family name
		(e.g. DB8500).

What:		/sys/devices/socX/serial_number
Date:		January 2019
contact:	<PERSON><PERSON><PERSON> <bjorn.and<PERSON><PERSON>@linaro.org>
Description:
		Read-only attribute supported by most SoCs. Contains the SoC's
		serial number, if available.

What:		/sys/devices/socX/soc_id
Date:		January 2012
contact:	<PERSON> <<EMAIL>>
Description:
		Read-only attribute supported by most SoCs. In the case of
		ST-<PERSON>'s chips this contains the SoC serial number.

What:		/sys/devices/socX/revision
Date:		January 2012
contact:	Lee Jones <<EMAIL>>
Description:
		Read-only attribute supported by most SoCs. Contains the SoC's
		manufacturing revision number.

What:		/sys/devices/socX/process
Date:		January 2012
contact:	Lee Jones <<EMAIL>>
Description:
		Read-only attribute supported ST-Ericsson's silicon. Contains the
		the process by which the silicon chip was manufactured.

What:		/sys/bus/soc
Date:		January 2012
contact:	Lee Jones <<EMAIL>>
Description:
		The /sys/bus/soc/ directory contains the usual sub-folders
		expected under most buses. /sys/bus/soc/devices is of particular
		interest, as it contains a symlink for each SoC device found on
		the system. Each symlink points back into the aforementioned
		/sys/devices/socX devices.
