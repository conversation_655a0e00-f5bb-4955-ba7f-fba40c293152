What:		/sys/bus/acpi/intel-rapid-start/wakeup_events
Date:		July 2, 2013
KernelVersion:	3.11
Contact:	<PERSON> <<EMAIL>>
Description:	An integer representing a set of wakeup events as follows:
		1: Wake to enter hibernation when the wakeup timer expires
		2: Wake to enter hibernation when the battery reaches a
		critical level

		These values are ORed together. For example, a value of 3
		indicates that the system will wake to enter hibernation when
		either the wakeup timer expires or the battery reaches a
		critical level.

What:		/sys/bus/acpi/intel-rapid-start/wakeup_time
Date:		July 2, 2013
KernelVersion:	3.11
Contact:	<PERSON> <<EMAIL>>
Description:	An integer representing the length of time the system will
		remain asleep before waking up to enter hibernation.
		This value is in minutes.
