What:		/sys/class/leds/<led>/brightness
Date:		March 2020
KernelVersion:	5.9
Contact:	<PERSON> <<EMAIL>>
Description:	read/write
		Writing to this file will update all LEDs within the group to a
		calculated percentage of what each color LED intensity is set
		to. The percentage is calculated for each grouped LED via the
		equation below:

		led_brightness = brightness * multi_intensity/max_brightness

		For additional details please refer to
		Documentation/leds/leds-class-multicolor.rst.

		The value of the LED is from 0 to
		/sys/class/leds/<led>/max_brightness.

What:		/sys/class/leds/<led>/multi_index
Date:		March 2020
KernelVersion:	5.9
Contact:	<PERSON> <<EMAIL>>
Description:	read
		The multi_index array, when read, will output the LED colors
		as an array of strings as they are indexed in the
		multi_intensity file.

What:		/sys/class/leds/<led>/multi_intensity
Date:		March 2020
KernelVersion:	5.9
Contact:	<PERSON> <<EMAIL>>
Description:	read/write
		This file contains array of integers. Order of components is
		described by the multi_index array. The maximum intensity should
		not exceed /sys/class/leds/<led>/max_brightness.
