What:		/sys/class/leds/SRWS1::<serial>::RPM1
What:		/sys/class/leds/SRWS1::<serial>::RPM2
What:		/sys/class/leds/SRWS1::<serial>::RPM3
What:		/sys/class/leds/SRWS1::<serial>::RPM4
What:		/sys/class/leds/SRWS1::<serial>::RPM5
What:		/sys/class/leds/SRWS1::<serial>::RPM6
What:		/sys/class/leds/SRWS1::<serial>::RPM7
What:		/sys/class/leds/SRWS1::<serial>::RPM8
What:		/sys/class/leds/SRWS1::<serial>::RPM9
What:		/sys/class/leds/SRWS1::<serial>::RPM10
What:		/sys/class/leds/SRWS1::<serial>::RPM11
What:		/sys/class/leds/SRWS1::<serial>::RPM12
What:		/sys/class/leds/SRWS1::<serial>::RPM13
What:		/sys/class/leds/SRWS1::<serial>::RPM14
What:		/sys/class/leds/SRWS1::<serial>::RPM15
What:		/sys/class/leds/SRWS1::<serial>::RPMALL
Date:		Jan 2013
KernelVersion:	3.9
Contact:	Simon Wood <<EMAIL>>
Description:	Provides a control for turning on/off the LEDs which form
		an RPM meter on the front of the controller
