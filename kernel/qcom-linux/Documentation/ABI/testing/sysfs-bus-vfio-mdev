What:           /sys/.../<device>/mdev_supported_types/
Date:           October 2016
Contact:        <PERSON><PERSON><PERSON> <<EMAIL>>
Description:
                This directory contains list of directories of currently
		supported mediated device types and their details for
		<device>. Supported type attributes are defined by the
		vendor driver who registers with Mediated device framework.
		Each supported type is a directory whose name is created
		by adding the device driver string as a prefix to the
		string provided by the vendor driver.

What:           /sys/.../<device>/mdev_supported_types/<type-id>/
Date:           October 2016
Contact:        <PERSON><PERSON><PERSON> <<EMAIL>>
Description:
                This directory gives details of supported type, like name,
		description, available_instances, device_api etc.
		'device_api' and 'available_instances' are mandatory
		attributes to be provided by vendor driver. 'name',
		'description' and other vendor driver specific attributes
		are optional.

What:           /sys/.../mdev_supported_types/<type-id>/create
Date:           October 2016
Contact:        <PERSON><PERSON><PERSON> <kwan<PERSON>@nvidia.com>
Description:
		Writing UUID to this file will create mediated device of
		type <type-id> for parent device <device>. This is a
		write-only file.
		For example:
		# echo "83b8f4f2-509f-382f-3c1e-e6bfe0fa1001" >	\
		       /sys/devices/foo/mdev_supported_types/foo-1/create

What:           /sys/.../mdev_supported_types/<type-id>/devices/
Date:           October 2016
Contact:        Kirti <PERSON>khede <<EMAIL>>
Description:
		This directory contains symbolic links pointing to mdev
		devices sysfs entries which are created of this <type-id>.

What:           /sys/.../mdev_supported_types/<type-id>/available_instances
Date:           October 2016
Contact:        Kirti Wankhede <<EMAIL>>
Description:
		Reading this attribute will show the number of mediated
		devices of type <type-id> that can be created. This is a
		readonly file.
Users:
		Userspace applications interested in creating mediated
		device of that type. Userspace application should check
		the number of available instances could be created before
		creating mediated device of this type.

What:           /sys/.../mdev_supported_types/<type-id>/device_api
Date:           October 2016
Contact:        Kirti Wankhede <<EMAIL>>
Description:
		Reading this attribute will show VFIO device API supported
		by this type. For example, "vfio-pci" for a PCI device,
		"vfio-platform" for platform device.

What:           /sys/.../mdev_supported_types/<type-id>/name
Date:           October 2016
Contact:        Kirti Wankhede <<EMAIL>>
Description:
		Reading this attribute will show human readable name of the
		mediated device that will get created of type <type-id>.
		This is optional attribute. For example: "Grid M60-0Q"
Users:
		Userspace applications interested in knowing the name of
		a particular <type-id> that can help in understanding the
		type of mediated device.

What:           /sys/.../mdev_supported_types/<type-id>/description
Date:           October 2016
Contact:        Kirti Wankhede <<EMAIL>>
Description:
		Reading this attribute will show description of the type of
		mediated device that will get created of type <type-id>.
		This is optional attribute. For example:
		"2 heads, 512M FB, 2560x1600 maximum resolution"
Users:
		Userspace applications interested in knowing the details of
		a particular <type-id> that can help in understanding the
		features provided by that type of mediated device.

What:           /sys/.../<device>/<UUID>/
Date:           October 2016
Contact:        Kirti Wankhede <<EMAIL>>
Description:
		This directory represents device directory of mediated
		device. It contains all the attributes related to mediated
		device.

What:           /sys/.../<device>/<UUID>/mdev_type
Date:           October 2016
Contact:        Kirti Wankhede <<EMAIL>>
Description:
		This is symbolic link pointing to supported type, <type-id>
		directory of which this mediated device is created.

What:           /sys/.../<device>/<UUID>/remove
Date:           October 2016
Contact:        Kirti Wankhede <<EMAIL>>
Description:
		Writing '1' to this file destroys the mediated device. The
		vendor driver can fail the remove() callback if that device
		is active and the vendor driver doesn't support hot unplug.
		Example:
		# echo 1 > /sys/bus/mdev/devices/<UUID>/remove
