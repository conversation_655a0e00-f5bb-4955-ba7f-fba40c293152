What:		/sys/devices/system/cpu/
Date:		pre-git history
Contact:	Linux kernel mailing list <<EMAIL>>
Description:
		A collection of both global and individual CPU attributes

		Individual CPU attributes are contained in subdirectories
		named by the kernel's logical CPU number, e.g.:

		/sys/devices/system/cpu/cpu#/

What:		/sys/devices/system/cpu/kernel_max
		/sys/devices/system/cpu/offline
		/sys/devices/system/cpu/online
		/sys/devices/system/cpu/possible
		/sys/devices/system/cpu/present
Date:		December 2008
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	CPU topology files that describe kernel limits related to
		hotplug. Briefly:

		kernel_max: the maximum cpu index allowed by the kernel
		configuration.

		offline: cpus that are not online because they have been
		HOTPLUGGED off or exceed the limit of cpus allowed by the
		kernel configuration (kernel_max above).

		online: cpus that are online and being scheduled.

		possible: cpus that have been allocated resources and can be
		brought online if they are present.

		present: cpus that have been identified as being present in
		the system.

		See Documentation/admin-guide/cputopology.rst for more information.


What:		/sys/devices/system/cpu/probe
		/sys/devices/system/cpu/release
Date:		November 2009
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Dynamic addition and removal of CPU's.  This is not hotplug
		removal, this is meant complete removal/addition of the CPU
		from the system.

		probe: writes to this file will dynamically add a CPU to the
		system.  Information written to the file to add CPU's is
		architecture specific.

		release: writes to this file dynamically remove a CPU from
		the system.  Information writtento the file to remove CPU's
		is architecture specific.

What:		/sys/devices/system/cpu/cpu#/node
Date:		October 2009
Contact:	Linux memory management mailing list <<EMAIL>>
Description:	Discover NUMA node a CPU belongs to

		When CONFIG_NUMA is enabled, a symbolic link that points
		to the corresponding NUMA node directory.

		For example, the following symlink is created for cpu42
		in NUMA node 2:

		/sys/devices/system/cpu/cpu42/node2 -> ../../node/node2


What:		/sys/devices/system/cpu/cpu#/topology/core_id
		/sys/devices/system/cpu/cpu#/topology/core_siblings
		/sys/devices/system/cpu/cpu#/topology/core_siblings_list
		/sys/devices/system/cpu/cpu#/topology/physical_package_id
		/sys/devices/system/cpu/cpu#/topology/thread_siblings
		/sys/devices/system/cpu/cpu#/topology/thread_siblings_list
Date:		December 2008
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	CPU topology files that describe a logical CPU's relationship
		to other cores and threads in the same physical package.

		One cpu# directory is created per logical CPU in the system,
		e.g. /sys/devices/system/cpu/cpu42/.

		Briefly, the files above are:

		core_id: the CPU core ID of cpu#. Typically it is the
		hardware platform's identifier (rather than the kernel's).
		The actual value is architecture and platform dependent.

		core_siblings: internal kernel map of cpu#'s hardware threads
		within the same physical_package_id.

		core_siblings_list: human-readable list of the logical CPU
		numbers within the same physical_package_id as cpu#.

		physical_package_id: physical package id of cpu#. Typically
		corresponds to a physical socket number, but the actual value
		is architecture and platform dependent.

		thread_siblings: internel kernel map of cpu#'s hardware
		threads within the same core as cpu#

		thread_siblings_list: human-readable list of cpu#'s hardware
		threads within the same core as cpu#

		See Documentation/admin-guide/cputopology.rst for more information.


What:		/sys/devices/system/cpu/cpuidle/current_driver
		/sys/devices/system/cpu/cpuidle/current_governer_ro
		/sys/devices/system/cpu/cpuidle/available_governors
		/sys/devices/system/cpu/cpuidle/current_governor
Date:		September 2007
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Discover cpuidle policy and mechanism

		Various CPUs today support multiple idle levels that are
		differentiated by varying exit latencies and power
		consumption during idle.

		Idle policy (governor) is differentiated from idle mechanism
		(driver)

		current_driver: (RO) displays current idle mechanism

		current_governor_ro: (RO) displays current idle policy

		With the cpuidle_sysfs_switch boot option enabled (meant for
		developer testing), the following three attributes are visible
		instead:

		current_driver: same as described above

		available_governors: (RO) displays a space separated list of
		available governors

		current_governor: (RW) displays current idle policy. Users can
		switch the governor at runtime by writing to this file.

		See Documentation/admin-guide/pm/cpuidle.rst and
		Documentation/driver-api/pm/cpuidle.rst for more information.


What:		/sys/devices/system/cpu/cpuX/cpuidle/stateN/name
		/sys/devices/system/cpu/cpuX/cpuidle/stateN/latency
		/sys/devices/system/cpu/cpuX/cpuidle/stateN/power
		/sys/devices/system/cpu/cpuX/cpuidle/stateN/time
		/sys/devices/system/cpu/cpuX/cpuidle/stateN/usage
		/sys/devices/system/cpu/cpuX/cpuidle/stateN/above
		/sys/devices/system/cpu/cpuX/cpuidle/stateN/below
Date:		September 2007
KernelVersion:	v2.6.24
Contact:	Linux power management list <<EMAIL>>
Description:
		The directory /sys/devices/system/cpu/cpuX/cpuidle contains per
		logical CPU specific cpuidle information for each online cpu X.
		The processor idle states which are available for use have the
		following attributes:

		name: (RO) Name of the idle state (string).

		latency: (RO) The latency to exit out of this idle state (in
		microseconds).

		power: (RO) The power consumed while in this idle state (in
		milliwatts).

		time: (RO) The total time spent in this idle state (in microseconds).

		usage: (RO) Number of times this state was entered (a count).

		above: (RO) Number of times this state was entered, but the
		       observed CPU idle duration was too short for it (a count).

		below: (RO) Number of times this state was entered, but the
		       observed CPU idle duration was too long for it (a count).

What:		/sys/devices/system/cpu/cpuX/cpuidle/stateN/desc
Date:		February 2008
KernelVersion:	v2.6.25
Contact:	Linux power management list <<EMAIL>>
Description:
		(RO) A small description about the idle state (string).


What:		/sys/devices/system/cpu/cpuX/cpuidle/stateN/disable
Date:		March 2012
KernelVersion:	v3.10
Contact:	Linux power management list <<EMAIL>>
Description:
		(RW) Option to disable this idle state (bool). The behavior and
		the effect of the disable variable depends on the implementation
		of a particular governor. In the ladder governor, for example,
		it is not coherent, i.e. if one is disabling a light state, then
		all deeper states are disabled as well, but the disable variable
		does not reflect it. Likewise, if one enables a deep state but a
		lighter state still is disabled, then this has no effect.


What:		/sys/devices/system/cpu/cpuX/cpuidle/stateN/residency
Date:		March 2014
KernelVersion:	v3.15
Contact:	Linux power management list <<EMAIL>>
Description:
		(RO) Display the target residency i.e. the minimum amount of
		time (in microseconds) this cpu should spend in this idle state
		to make the transition worth the effort.

What:		/sys/devices/system/cpu/cpuX/cpuidle/stateN/s2idle/
Date:		March 2018
KernelVersion:	v4.17
Contact:	Linux power management list <<EMAIL>>
Description:
		Idle state usage statistics related to suspend-to-idle.

		This attribute group is only present for states that can be
		used in suspend-to-idle with suspended timekeeping.

What:		/sys/devices/system/cpu/cpuX/cpuidle/stateN/s2idle/time
Date:		March 2018
KernelVersion:	v4.17
Contact:	Linux power management list <<EMAIL>>
Description:
		Total time spent by the CPU in suspend-to-idle (with scheduler
		tick suspended) after requesting this state.

What:		/sys/devices/system/cpu/cpuX/cpuidle/stateN/s2idle/usage
Date:		March 2018
KernelVersion:	v4.17
Contact:	Linux power management list <<EMAIL>>
Description:
		Total number of times this state has been requested by the CPU
		while entering suspend-to-idle.

What:		/sys/devices/system/cpu/cpu#/cpufreq/*
Date:		pre-git history
Contact:	<EMAIL>
Description:	Discover and change clock speed of CPUs

		Clock scaling allows you to change the clock speed of the
		CPUs on the fly. This is a nice method to save battery
		power, because the lower the clock speed, the less power
		the CPU consumes.

		There are many knobs to tweak in this directory.

		See files in Documentation/cpu-freq/ for more information.


What:		/sys/devices/system/cpu/cpu#/cpufreq/freqdomain_cpus
Date:		June 2013
Contact:	<EMAIL>
Description:	Discover CPUs in the same CPU frequency coordination domain

		freqdomain_cpus is the list of CPUs (online+offline) that share
		the same clock/freq domain (possibly at the hardware level).
		That information may be hidden from the cpufreq core and the
		value of related_cpus may be different from freqdomain_cpus. This
		attribute is useful for user space DVFS controllers to get better
		power/performance results for platforms using acpi-cpufreq.

		This file is only present if the acpi-cpufreq driver is in use.


What:		/sys/devices/system/cpu/cpu*/cache/index3/cache_disable_{0,1}
Date:		August 2008
KernelVersion:	2.6.27
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Disable L3 cache indices

		These files exist in every CPU's cache/index3 directory. Each
		cache_disable_{0,1} file corresponds to one disable slot which
		can be used to disable a cache index. Reading from these files
		on a processor with this functionality will return the currently
		disabled index for that node. There is one L3 structure per
		node, or per internal node on MCM machines. Writing a valid
		index to one of these files will cause the specificed cache
		index to be disabled.

		All AMD processors with L3 caches provide this functionality.
		For details, see BKDGs at
		http://developer.amd.com/documentation/guides/Pages/default.aspx


What:		/sys/devices/system/cpu/cpufreq/boost
Date:		August 2012
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Processor frequency boosting control

		This switch controls the boost setting for the whole system.
		Boosting allows the CPU and the firmware to run at a frequency
		beyound it's nominal limit.
		More details can be found in
		Documentation/admin-guide/pm/cpufreq.rst


What:		/sys/devices/system/cpu/cpu#/crash_notes
		/sys/devices/system/cpu/cpu#/crash_notes_size
Date:		April 2013
Contact:	<EMAIL>
Description:	address and size of the percpu note.

		crash_notes: the physical address of the memory that holds the
		note of cpu#.

		crash_notes_size: size of the note of cpu#.


What:		/sys/devices/system/cpu/intel_pstate/max_perf_pct
		/sys/devices/system/cpu/intel_pstate/min_perf_pct
		/sys/devices/system/cpu/intel_pstate/no_turbo
Date:		February 2013
Contact:	<EMAIL>
Description:	Parameters for the Intel P-state driver

		Logic for selecting the current P-state in Intel
		Sandybridge+ processors. The three knobs control
		limits for the P-state that will be requested by the
		driver.

		max_perf_pct: limits the maximum P state that will be requested by
		the driver stated as a percentage of the available performance.

		min_perf_pct: limits the minimum P state that will be requested by
		the driver stated as a percentage of the available performance.

		no_turbo: limits the driver to selecting P states below the turbo
		frequency range.

		More details can be found in
		Documentation/admin-guide/pm/intel_pstate.rst

What:		/sys/devices/system/cpu/cpu*/cache/index*/<set_of_attributes_mentioned_below>
Date:		July 2014(documented, existed before August 2008)
Contact:	Sudeep Holla <<EMAIL>>
		Linux kernel mailing list <<EMAIL>>
Description:	Parameters for the CPU cache attributes

		allocation_policy:
			- WriteAllocate: allocate a memory location to a cache line
					 on a cache miss because of a write
			- ReadAllocate: allocate a memory location to a cache line
					on a cache miss because of a read
			- ReadWriteAllocate: both writeallocate and readallocate

		attributes: LEGACY used only on IA64 and is same as write_policy

		coherency_line_size: the minimum amount of data in bytes that gets
				     transferred from memory to cache

		level: the cache hierarchy in the multi-level cache configuration

		number_of_sets: total number of sets in the cache, a set is a
				collection of cache lines with the same cache index

		physical_line_partition: number of physical cache line per cache tag

		shared_cpu_list: the list of logical cpus sharing the cache

		shared_cpu_map: logical cpu mask containing the list of cpus sharing
				the cache

		size: the total cache size in kB

		type:
			- Instruction: cache that only holds instructions
			- Data: cache that only caches data
			- Unified: cache that holds both data and instructions

		ways_of_associativity: degree of freedom in placing a particular block
					of memory in the cache

		write_policy:
			- WriteThrough: data is written to both the cache line
					and to the block in the lower-level memory
			- WriteBack: data is written only to the cache line and
				     the modified cache line is written to main
				     memory only when it is replaced


What:		/sys/devices/system/cpu/cpu*/cache/index*/id
Date:		September 2016
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Cache id

		The id provides a unique number for a specific instance of
		a cache of a particular type. E.g. there may be a level
		3 unified cache on each socket in a server and we may
		assign them ids 0, 1, 2, ...

		Note that id value can be non-contiguous. E.g. level 1
		caches typically exist per core, but there may not be a
		power of two cores on a socket, so these caches may be
		numbered 0, 1, 2, 3, 4, 5, 8, 9, 10, ...

What:		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/turbo_stat
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/sub_turbo_stat
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/unthrottle
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/powercap
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/overtemp
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/supply_fault
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/overcurrent
		/sys/devices/system/cpu/cpuX/cpufreq/throttle_stats/occ_reset
Date:		March 2016
Contact:	Linux kernel mailing list <<EMAIL>>
		Linux for PowerPC mailing list <<EMAIL>>
Description:	POWERNV CPUFreq driver's frequency throttle stats directory and
		attributes

		'cpuX/cpufreq/throttle_stats' directory contains the CPU frequency
		throttle stat attributes for the chip. The throttle stats of a cpu
		is common across all the cpus belonging to a chip. Below are the
		throttle attributes exported in the 'throttle_stats' directory:

		- turbo_stat : This file gives the total number of times the max
		frequency is throttled to lower frequency in turbo (at and above
		nominal frequency) range of frequencies.

		- sub_turbo_stat : This file gives the total number of times the
		max frequency is throttled to lower frequency in sub-turbo(below
		nominal frequency) range of frequencies.

		- unthrottle : This file gives the total number of times the max
		frequency is unthrottled after being throttled.

		- powercap : This file gives the total number of times the max
		frequency is throttled due to 'Power Capping'.

		- overtemp : This file gives the total number of times the max
		frequency is throttled due to 'CPU Over Temperature'.

		- supply_fault : This file gives the total number of times the
		max frequency is throttled due to 'Power Supply Failure'.

		- overcurrent : This file gives the total number of times the
		max frequency is throttled due to 'Overcurrent'.

		- occ_reset : This file gives the total number of times the max
		frequency is throttled due to 'OCC Reset'.

		The sysfs attributes representing different throttle reasons like
		powercap, overtemp, supply_fault, overcurrent and occ_reset map to
		the reasons provided by OCC firmware for throttling the frequency.

What:		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/turbo_stat
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/sub_turbo_stat
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/unthrottle
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/powercap
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/overtemp
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/supply_fault
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/overcurrent
		/sys/devices/system/cpu/cpufreq/policyX/throttle_stats/occ_reset
Date:		March 2016
Contact:	Linux kernel mailing list <<EMAIL>>
		Linux for PowerPC mailing list <<EMAIL>>
Description:	POWERNV CPUFreq driver's frequency throttle stats directory and
		attributes

		'policyX/throttle_stats' directory and all the attributes are same as
		the /sys/devices/system/cpu/cpuX/cpufreq/throttle_stats directory and
		attributes which give the frequency throttle information of the chip.

What:		/sys/devices/system/cpu/cpuX/regs/
		/sys/devices/system/cpu/cpuX/regs/identification/
		/sys/devices/system/cpu/cpuX/regs/identification/midr_el1
		/sys/devices/system/cpu/cpuX/regs/identification/revidr_el1
Date:		June 2016
Contact:	Linux ARM Kernel Mailing list <<EMAIL>>
Description:	AArch64 CPU registers
		'identification' directory exposes the CPU ID registers for
		 identifying model and revision of the CPU.

What:		/sys/devices/system/cpu/cpu#/cpu_capacity
Date:		December 2016
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	information about CPUs heterogeneity.

		cpu_capacity: capacity of cpu#.

What:		/sys/devices/system/cpu/vulnerabilities
		/sys/devices/system/cpu/vulnerabilities/meltdown
		/sys/devices/system/cpu/vulnerabilities/spectre_v1
		/sys/devices/system/cpu/vulnerabilities/spectre_v2
		/sys/devices/system/cpu/vulnerabilities/spec_store_bypass
		/sys/devices/system/cpu/vulnerabilities/l1tf
		/sys/devices/system/cpu/vulnerabilities/mds
		/sys/devices/system/cpu/vulnerabilities/srbds
		/sys/devices/system/cpu/vulnerabilities/tsx_async_abort
		/sys/devices/system/cpu/vulnerabilities/itlb_multihit
Date:		January 2018
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Information about CPU vulnerabilities

		The files are named after the code names of CPU
		vulnerabilities. The output of those files reflects the
		state of the CPUs in the system. Possible output values:

		"Not affected"	  CPU is not affected by the vulnerability
		"Vulnerable"	  CPU is affected and no mitigation in effect
		"Mitigation: $M"  CPU is affected and mitigation $M is in effect

		See also: Documentation/admin-guide/hw-vuln/index.rst

What:		/sys/devices/system/cpu/smt
		/sys/devices/system/cpu/smt/active
		/sys/devices/system/cpu/smt/control
Date:		June 2018
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Control Symetric Multi Threading (SMT)

		active:  Tells whether SMT is active (enabled and siblings online)

		control: Read/write interface to control SMT. Possible
			 values:

			 "on"		  SMT is enabled
			 "off"		  SMT is disabled
			 "forceoff"	  SMT is force disabled. Cannot be changed.
			 "notsupported"   SMT is not supported by the CPU
			 "notimplemented" SMT runtime toggling is not
					  implemented for the architecture

			 If control status is "forceoff" or "notsupported" writes
			 are rejected.

What:		/sys/devices/system/cpu/cpu#/power/energy_perf_bias
Date:		March 2019
Contact:	<EMAIL>
Description:	Intel Energy and Performance Bias Hint (EPB)

		EPB for the given CPU in a sliding scale 0 - 15, where a value
		of 0 corresponds to a hint preference for highest performance
		and a value of 15 corresponds to the maximum energy savings.

		In order to change the EPB value for the CPU, write either
		a number in the 0 - 15 sliding scale above, or one of the
		strings: "performance", "balance-performance", "normal",
		"balance-power", "power" (that represent values reflected by
		their meaning), to this attribute.

		This attribute is present for all online CPUs supporting the
		Intel EPB feature.

What:		/sys/devices/system/cpu/umwait_control
		/sys/devices/system/cpu/umwait_control/enable_c02
		/sys/devices/system/cpu/umwait_control/max_time
Date:		May 2019
Contact:	Linux kernel mailing list <<EMAIL>>
Description:	Umwait control

		enable_c02: Read/write interface to control umwait C0.2 state
			Read returns C0.2 state status:
				0: C0.2 is disabled
				1: C0.2 is enabled

			Write 'y' or '1'  or 'on' to enable C0.2 state.
			Write 'n' or '0'  or 'off' to disable C0.2 state.

			The interface is case insensitive.

		max_time: Read/write interface to control umwait maximum time
			  in TSC-quanta that the CPU can reside in either C0.1
			  or C0.2 state. The time is an unsigned 32-bit number.
			  Note that a value of zero means there is no limit.
			  Low order two bits must be zero.

What:		/sys/devices/system/cpu/svm
Date:		August 2019
Contact:	Linux kernel mailing list <<EMAIL>>
		Linux for PowerPC mailing list <<EMAIL>>
Description:	Secure Virtual Machine

		If 1, it means the system is using the Protected Execution
		Facility in POWER9 and newer processors. i.e., it is a Secure
		Virtual Machine.
