What:		/sys/class/tty/console/active
Date:		Nov 2010
Contact:	<PERSON> <<EMAIL>>
Description:
		 Shows the list of currently configured
		 console devices, like 'tty1 ttyS0'.
		 The last entry in the file is the active
		 device connected to /dev/console.
		 The file supports poll() to detect virtual
		 console switches.

What:		/sys/class/tty/tty0/active
Date:		Nov 2010
Contact:	<PERSON> <<EMAIL>>
Description:
		 Shows the currently active virtual console
		 device, like 'tty1'.
		 The file supports poll() to detect virtual
		 console switches.

What:		/sys/class/tty/ttyS0/uartclk
Date:		Sep 2012
Contact:	<PERSON> <<EMAIL>>
Description:
		 Shows the current uartclk value associated with the
		 UART port in serial_core, that is bound to TTY like ttyS0.
		 uartclk = 16 * baud_base

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/type
Date:		October 2012
Contact:	<PERSON> <<EMAIL>>
Description:
		 Shows the current tty type for this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/line
Date:		October 2012
Contact:	<PERSON> <PERSON> <<EMAIL>>
Description:
		 Shows the current tty line number for this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/port
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Shows the current tty port I/O address for this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/irq
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Shows the current primary interrupt for this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/flags
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Show the tty port status flags for this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/xmit_fifo_size
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Show the transmit FIFO size for this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/close_delay
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Show the closing delay time for this port in ms.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/closing_wait
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Show the close wait time for this port in ms.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/custom_divisor
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Show the custom divisor if any that is set on this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/io_type
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Show the I/O type that is to be used with the iomem base
		 address.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/iomem_base
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 The I/O memory base for this port.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/iomem_reg_shift
Date:		October 2012
Contact:	Alan Cox <<EMAIL>>
Description:
		 Show the register shift indicating the spacing to be used
		 for accesses on this iomem address.

		 These sysfs values expose the TIOCGSERIAL interface via
		 sysfs rather than via ioctls.

What:		/sys/class/tty/ttyS0/rx_trig_bytes
Date:		May 2014
Contact:	Yoshihiro YUNOMAE <<EMAIL>>
Description:
		 Shows current RX interrupt trigger bytes or sets the
		 user specified value to change it for the FIFO buffer.
		 Users can show or set this value regardless of opening the
		 serial device file or not.

		 The RX trigger can be set one of four kinds of values for UART
		 serials. When users input a meaning less value to this I/F,
		 the RX trigger is changed to the nearest lower value for the
		 device specification. For example, when user sets 7bytes on
		 16550A, which has 1/4/8/14 bytes trigger, the RX trigger is
		 automatically changed to 4 bytes.
