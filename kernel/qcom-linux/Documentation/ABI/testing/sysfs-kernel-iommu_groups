What:		/sys/kernel/iommu_groups/
Date:		May 2012
KernelVersion:	v3.5
Contact:	<PERSON> <<EMAIL>>
Description:	/sys/kernel/iommu_groups/ contains a number of sub-
		directories, each representing an IOMMU group.  The
		name of the sub-directory matches the iommu_group_id()
		for the group, which is an integer value.  Within each
		subdirectory is another directory named "devices" with
		links to the sysfs devices contained in this group.
		The group directory also optionally contains a "name"
		file if the IOMMU driver has chosen to register a more
		common name for the group.
Users:

What:		/sys/kernel/iommu_groups/reserved_regions
Date: 		January 2017
KernelVersion:  v4.11
Contact: 	<PERSON> <<EMAIL>>
Description:    /sys/kernel/iommu_groups/reserved_regions list IOVA
		regions that are reserved. Not necessarily all
		reserved regions are listed. This is typically used to
		output direct-mapped, MSI, non mappable regions. Each
		region is described on a single line: the 1st field is
		the base IOVA, the second is the end IOVA and the third
		field describes the type of the region.

What:		/sys/kernel/iommu_groups/reserved_regions
Date: 		June 2019
KernelVersion:  v5.3
Contact: 	<PERSON> <<EMAIL>>
Description:    In case an RMRR is used only by graphics or USB devices
		it is now exposed as "direct-relaxable" instead of "direct".
		In device assignment use case, for instance, those RMRR
		are considered to be relaxable and safe.
