What:		/sys/class/bsr/bsr*/bsr_size
Date:		Jul, 2008
KernelVersion:	2.6.27
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>,
		<PERSON> <<EMAIL>>
Description:
		(RO) Size of the barrier-synchronization register (BSR)
		register in bytes.

What:		/sys/class/bsr/bsr*/bsr_length
Date:		Jul, 2008
KernelVersion:	2.6.27
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>,
		<PERSON> <<EMAIL>>
Description:
		(RO) The length of memory region that can be mapped in bytes.

What:		/sys/class/bsr/bsr*/bsr_stride
Date:		Jul, 2008
KernelVersion:	2.6.27
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>,
		<PERSON> <<EMAIL>>
Description:
		(RO) The stride or the interval at which the allocated BSR bytes
		repeat within the mapping.
