USB Type-C port devices (eg. /sys/class/typec/port0/)

What:		/sys/class/typec/<port>/data_role
Date:		April 2017
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The supported USB data roles. This attribute can be used for
		requesting data role swapping on the port. Swapping is supported
		as synchronous operation, so write(2) to the attribute will not
		return until the operation has finished. The attribute is
		notified about role changes so that poll(2) on the attribute
		wakes up. Change on the role will also generate uevent
		KOBJ_CHANGE on the port. The current role is show in brackets,
		for example "[host] device" when DRP port is in host mode.

		Valid values: host, device

What:		/sys/class/typec/<port>/power_role
Date:		April 2017
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The supported power roles. This attribute can be used to request
		power role swap on the port when the port supports USB Power
		Delivery. Swapping is supported as synchronous operation, so
		write(2) to the attribute will not return until the operation
		has finished. The attribute is notified about role changes so
		that poll(2) on the attribute wakes up. Change on the role will
		also generate uevent KOBJ_CHANGE. The current role is show in
		brackets, for example "[source] sink" when in source mode.

		Valid values: source, sink

What:           /sys/class/typec/<port>/port_type
Date:           May 2017
Contact:	Badhri Jagan Sridharan <<EMAIL>>
Description:
		Indicates the type of the port. This attribute can be used for
		requesting a change in the port type. Port type change is
		supported as a synchronous operation, so write(2) to the
		attribute will not return until the operation has finished.

		Valid values:
		- source (The port will behave as source only DFP port)
		- sink (The port will behave as sink only UFP port)
		- dual (The port will behave as dual-role-data and
			dual-role-power port)

What:		/sys/class/typec/<port>/vconn_source
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows is the port VCONN Source. This attribute can be used to
		request VCONN swap to change the VCONN Source during connection
		when both the port and the partner support USB Power Delivery.
		Swapping is supported as synchronous operation, so write(2) to
		the attribute will not return until the operation has finished.
		The attribute is notified about VCONN source changes so that
		poll(2) on the attribute wakes up. Change on VCONN source also
		generates uevent KOBJ_CHANGE.

		Valid values:
		- "no" when the port is not the VCONN Source
		- "yes" when the port is the VCONN Source

What:		/sys/class/typec/<port>/power_operation_mode
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows the current power operational mode the port is in. The
		power operation mode means current level for VBUS. In case USB
		Power Delivery communication is used for negotiating the levels,
		power operation mode should show "usb_power_delivery".

		Valid values:
		- default
		- 1.5A
		- 3.0A
		- usb_power_delivery

What:		/sys/class/typec/<port>/preferred_role
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		The user space can notify the driver about the preferred role.
		It should be handled as enabling of Try.SRC or Try.SNK, as
		defined in USB Type-C specification, in the port drivers. By
		default the preferred role should come from the platform.

		Valid values: source, sink, none (to remove preference)

What:		/sys/class/typec/<port>/supported_accessory_modes
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Space separated list of accessory modes, defined in the USB
		Type-C specification, the port supports.

What:		/sys/class/typec/<port>/usb_power_delivery_revision
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Revision number of the supported USB Power Delivery
		specification, or 0 when USB Power Delivery is not supported.

What:		/sys/class/typec/<port>/usb_typec_revision
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Revision number of the supported USB Type-C specification.


USB Type-C partner devices (eg. /sys/class/typec/port0-partner/)

What:		/sys/class/typec/<port>-partner/accessory_mode
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows the Accessory Mode name when the partner is an Accessory.
		The Accessory Modes are defined in USB Type-C Specification.

What:		/sys/class/typec/<port>-partner/supports_usb_power_delivery
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows if the partner supports USB Power Delivery communication:
		Valid values: yes, no

What:		/sys/class/typec/<port>-partner>/identity/
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This directory appears only if the port device driver is capable
		of showing the result of Discover Identity USB power delivery
		command. That will not always be possible even when USB power
		delivery is supported, for example when USB power delivery
		communication for the port is mostly handled in firmware. If the
		directory exists, it will have an attribute file for every VDO
		in Discover Identity command result.

What:		/sys/class/typec/<port>-partner/identity/id_header
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		ID Header VDO part of Discover Identity command result. The
		value will show 0 until Discover Identity command result becomes
		available. The value can be polled.

What:		/sys/class/typec/<port>-partner/identity/cert_stat
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Cert Stat VDO part of Discover Identity command result. The
		value will show 0 until Discover Identity command result becomes
		available. The value can be polled.

What:		/sys/class/typec/<port>-partner/identity/product
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Product VDO part of Discover Identity command result. The value
		will show 0 until Discover Identity command result becomes
		available. The value can be polled.


USB Type-C cable devices (eg. /sys/class/typec/port0-cable/)

Note: Electronically Marked Cables will have a device also for one cable plug
(eg. /sys/class/typec/port0-plug0). If the cable is active and has also SOP
Double Prime controller (USB Power Deliver specification ch. 2.4) it will have
second device also for the other plug. Both plugs may have alternate modes as
described in USB Type-C and USB Power Delivery specifications.

What:		/sys/class/typec/<port>-cable/type
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows if the cable is active.
		Valid values: active, passive

What:		/sys/class/typec/<port>-cable/plug_type
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Shows type of the plug on the cable:
		- type-a - Standard A
		- type-b - Standard B
		- type-c
		- captive

What:		/sys/class/typec/<port>-cable/identity/
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		This directory appears only if the port device driver is capable
		of showing the result of Discover Identity USB power delivery
		command. That will not always be possible even when USB power
		delivery is supported. If the directory exists, it will have an
		attribute for every VDO returned by Discover Identity command.

What:		/sys/class/typec/<port>-cable/identity/id_header
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		ID Header VDO part of Discover Identity command result. The
		value will show 0 until Discover Identity command result becomes
		available. The value can be polled.

What:		/sys/class/typec/<port>-cable/identity/cert_stat
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Cert Stat VDO part of Discover Identity command result. The
		value will show 0 until Discover Identity command result becomes
		available. The value can be polled.

What:		/sys/class/typec/<port>-cable/identity/product
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Product VDO part of Discover Identity command result. The value
		will show 0 until Discover Identity command result becomes
		available. The value can be polled.


USB Type-C port alternate mode devices.

What:		/sys/class/typec/<port>/<alt mode>/supported_roles
Date:		April 2017
Contact:	Heikki Krogerus <<EMAIL>>
Description:
		Space separated list of the supported roles.

		Valid values: source, sink
