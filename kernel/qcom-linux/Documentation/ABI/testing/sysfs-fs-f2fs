What:		/sys/fs/f2fs/<disk>/gc_max_sleep_time
Date:		July 2013
Contact:	"<PERSON><PERSON><PERSON>" <<EMAIL>>
Description:
		 Controls the maximun sleep time for gc_thread. Time
		 is in milliseconds.

What:		/sys/fs/f2fs/<disk>/gc_min_sleep_time
Date:		July 2013
Contact:	"<PERSON>jae <PERSON>" <<EMAIL>>
Description:
		 Controls the minimum sleep time for gc_thread. Time
		 is in milliseconds.

What:		/sys/fs/f2fs/<disk>/gc_no_gc_sleep_time
Date:		July 2013
Contact:	"<PERSON>jae <PERSON>" <<EMAIL>>
Description:
		 Controls the default sleep time for gc_thread. Time
		 is in milliseconds.

What:		/sys/fs/f2fs/<disk>/gc_idle
Date:		July 2013
Contact:	"Namjae Jeon" <<EMAIL>>
Description:
		 Controls the victim selection policy for garbage collection.

What:		/sys/fs/f2fs/<disk>/reclaim_segments
Date:		October 2013
Contact:	"<PERSON><PERSON><PERSON><PERSON>" <<EMAIL>>
Description:
		 Controls the issue rate of segment discard commands.

What:		/sys/fs/f2fs/<disk>/ipu_policy
Date:		November 2013
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the in-place-update policy.

What:		/sys/fs/f2fs/<disk>/min_ipu_util
Date:		November 2013
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the FS utilization condition for the in-place-update
		 policies.

What:		/sys/fs/f2fs/<disk>/min_fsync_blocks
Date:		September 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the dirty page count condition for the in-place-update
		 policies.

What:		/sys/fs/f2fs/<disk>/min_seq_blocks
Date:		August 2018
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the dirty page count condition for batched sequential
		 writes in ->writepages.


What:		/sys/fs/f2fs/<disk>/min_hot_blocks
Date:		March 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the dirty page count condition for redefining hot data.

What:		/sys/fs/f2fs/<disk>/min_ssr_sections
Date:		October 2017
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Controls the fee section threshold to trigger SSR allocation.

What:		/sys/fs/f2fs/<disk>/max_small_discards
Date:		November 2013
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the issue rate of small discard commands.

What:          /sys/fs/f2fs/<disk>/discard_granularity
Date:          July 2017
Contact:       "Chao Yu" <<EMAIL>>
Description:
		Controls discard granularity of inner discard thread, inner thread
		will not issue discards with size that is smaller than granularity.
		The unit size is one block, now only support configuring in range
		of [1, 512].

What:          /sys/fs/f2fs/<disk>/umount_discard_timeout
Date:          January 2019
Contact:       "Jaegeuk Kim" <<EMAIL>>
Description:
		Set timeout to issue discard commands during umount.
		Default: 5 secs

What:		/sys/fs/f2fs/<disk>/max_victim_search
Date:		January 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the number of trials to find a victim segment.

What:		/sys/fs/f2fs/<disk>/migration_granularity
Date:		October 2018
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Controls migration granularity of garbage collection on large
		 section, it can let GC move partial segment{s} of one section
		 in one GC cycle, so that dispersing heavy overhead GC to
		 multiple lightweight one.

What:		/sys/fs/f2fs/<disk>/dir_level
Date:		March 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the directory level for large directory.

What:		/sys/fs/f2fs/<disk>/ram_thresh
Date:		March 2014
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the memory footprint used by f2fs.

What:		/sys/fs/f2fs/<disk>/batched_trim_sections
Date:		February 2015
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the trimming rate in batch mode.
		 <deprecated>

What:		/sys/fs/f2fs/<disk>/cp_interval
Date:		October 2015
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the checkpoint timing.

What:		/sys/fs/f2fs/<disk>/idle_interval
Date:		January 2016
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls the idle timing for all paths other than
		 discard and gc path.

What:		/sys/fs/f2fs/<disk>/discard_idle_interval
Date:		September 2018
Contact:	"Chao Yu" <<EMAIL>>
Contact:	"Sahitya Tummala" <<EMAIL>>
Description:
		 Controls the idle timing for discard path.

What:		/sys/fs/f2fs/<disk>/gc_idle_interval
Date:		September 2018
Contact:	"Chao Yu" <<EMAIL>>
Contact:	"Sahitya Tummala" <<EMAIL>>
Description:
		 Controls the idle timing for gc path.

What:		/sys/fs/f2fs/<disk>/iostat_enable
Date:		August 2017
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Controls to enable/disable IO stat.

What:		/sys/fs/f2fs/<disk>/ra_nid_pages
Date:		October 2015
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Controls the count of nid pages to be readaheaded.

What:		/sys/fs/f2fs/<disk>/dirty_nats_ratio
Date:		January 2016
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Controls dirty nat entries ratio threshold, if current
		 ratio exceeds configured threshold, checkpoint will
		 be triggered for flushing dirty nat entries.

What:		/sys/fs/f2fs/<disk>/lifetime_write_kbytes
Date:		January 2016
Contact:	"Shuoran Liu" <<EMAIL>>
Description:
		 Shows total written kbytes issued to disk.

What:		/sys/fs/f2fs/<disk>/features
Date:		July 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Shows all enabled features in current device.

What:		/sys/fs/f2fs/<disk>/inject_rate
Date:		May 2016
Contact:	"Sheng Yong" <<EMAIL>>
Description:
		 Controls the injection rate.

What:		/sys/fs/f2fs/<disk>/inject_type
Date:		May 2016
Contact:	"Sheng Yong" <<EMAIL>>
Description:
		 Controls the injection type.

What:		/sys/fs/f2fs/<disk>/reserved_blocks
Date:		June 2017
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Controls target reserved blocks in system, the threshold
		 is soft, it could exceed current available user space.

What:		/sys/fs/f2fs/<disk>/current_reserved_blocks
Date:		October 2017
Contact:	"Yunlong Song" <<EMAIL>>
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Shows current reserved blocks in system, it may be temporarily
		 smaller than target_reserved_blocks, but will gradually
		 increase to target_reserved_blocks when more free blocks are
		 freed by user later.

What:		/sys/fs/f2fs/<disk>/gc_urgent
Date:		August 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Do background GC agressively

What:		/sys/fs/f2fs/<disk>/gc_urgent_sleep_time
Date:		August 2017
Contact:	"Jaegeuk Kim" <<EMAIL>>
Description:
		 Controls sleep time of GC urgent mode

What:		/sys/fs/f2fs/<disk>/readdir_ra
Date:		November 2017
Contact:	"Sheng Yong" <<EMAIL>>
Description:
		 Controls readahead inode block in readdir.

What:		/sys/fs/f2fs/<disk>/extension_list
Date:		Feburary 2018
Contact:	"Chao Yu" <<EMAIL>>
Description:
		 Used to control configure extension list:
		 - Query: cat /sys/fs/f2fs/<disk>/extension_list
		 - Add: echo '[h/c]extension' > /sys/fs/f2fs/<disk>/extension_list
		 - Del: echo '[h/c]!extension' > /sys/fs/f2fs/<disk>/extension_list
		 - [h] means add/del hot file extension
		 - [c] means add/del cold file extension

What:		/sys/fs/f2fs/<disk>/unusable
Date		April 2019
Contact:	"Daniel Rosenberg" <<EMAIL>>
Description:
		If checkpoint=disable, it displays the number of blocks that are unusable.
                If checkpoint=enable it displays the enumber of blocks that would be unusable
                if checkpoint=disable were to be set.

What:		/sys/fs/f2fs/<disk>/encoding
Date		July 2019
Contact:	"Daniel Rosenberg" <<EMAIL>>
Description:
		Displays name and version of the encoding set for the filesystem.
                If no encoding is set, displays (none)
