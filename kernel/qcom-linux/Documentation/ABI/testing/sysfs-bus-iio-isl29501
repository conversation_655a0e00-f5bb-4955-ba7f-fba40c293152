What:		/sys/bus/iio/devices/iio:deviceX/in_proximity0_agc_gain
What:		/sys/bus/iio/devices/iio:deviceX/in_proximity0_agc_gain_bias
KernelVersion:	4.18
Contact:	<EMAIL>
Description:
		This sensor has an automatic gain control (agc) loop
		which sets the analog signal levels at an optimum
		level by controlling programmable gain amplifiers. The
		criteria for optimal gain is determined by the sensor.

		Return the actual gain value as an integer in [0; 65536]
		range when read from.

		The agc gain read when measuring crosstalk shall be
		written into in_proximity0_agc_gain_bias.

What:		/sys/bus/iio/devices/iio:deviceX/in_proximity0_calib_phase_temp_a
What:		/sys/bus/iio/devices/iio:deviceX/in_proximity0_calib_phase_temp_b
What:		/sys/bus/iio/devices/iio:deviceX/in_proximity0_calib_phase_light_a
What:		/sys/bus/iio/devices/iio:deviceX/in_proximity0_calib_phase_light_b
KernelVersion:	4.18
Contact:	<EMAIL>
Description:
		The sensor is able to perform correction of distance
		measurements due to changing temperature and ambient
		light conditions. It can be programmed to correct for
		a second order error polynomial.

		Phase data has to be collected when temperature and
		ambient light are modulated independently.

		Then a least squares curve fit to a second order
		polynomial has to be generated from the data. The
		resultant curves have the form ax^2 + bx + c.

		From those two curves, a and b coefficients shall be
		stored in in_proximity0_calib_phase_temp_a and
		in_proximity0_calib_phase_temp_b for temperature and
		in in_proximity0_calib_phase_light_a and
		in_proximity0_calib_phase_light_b for ambient light.

		Those values must be integer in [0; 8355840] range.

		Finally, the c constant is set by the sensor
		internally.

		The value stored in sensor is displayed when read from.
