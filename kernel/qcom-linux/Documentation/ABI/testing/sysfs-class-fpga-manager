What:		/sys/class/fpga_manager/<fpga>/name
Date:		August 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	Name of low level fpga manager driver.

What:		/sys/class/fpga_manager/<fpga>/state
Date:		August 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	Read fpga manager state as a string.
		The intent is to provide enough detail that if something goes
		wrong during FPGA programming (something that the driver can't
		fix) then userspace can know, i.e. if the firmware request
		fails, that could be due to not being able to find the firmware
		file.

		This is a superset of FPGA states and fpga manager driver
		states.  The fpga manager driver is walking through these steps
		to get the FPGA into a known operating state.  It's a sequence,
		though some steps may get skipped.  Valid FPGA states will vary
		by manufacturer; this is a superset.

		* unknown		= can't determine state
		* power off		= FPGA power is off
		* power up		= FPGA reports power is up
		* reset			= FPGA held in reset state
		* firmware request	= firmware class request in progress
		* firmware request error = firmware request failed
		* write init		= preparing FPGA for programming
		* write init error	= Error while preparing FPGA for
					  programming
		* write			= FPGA ready to receive image data
		* write error		= Error while programming
		* write complete	= Doing post programming steps
		* write complete error	= Error while doing post programming
		* operating		= FPGA is programmed and operating

What:		/sys/class/fpga_manager/<fpga>/status
Date:		June 2018
KernelVersion:	4.19
Contact:	Wu Hao <<EMAIL>>
Description:	Read fpga manager status as a string.
		If FPGA programming operation fails, it could be caused by crc
		error or incompatible bitstream image. The intent of this
		interface is to provide more detailed information for FPGA
		programming errors to userspace. This is a list of strings for
		the supported status.

		* reconfig operation error 	- invalid operations detected by
						  reconfiguration hardware.
						  e.g. start reconfiguration
						  with errors not cleared
		* reconfig CRC error		- CRC error detected by
						  reconfiguration hardware.
		* reconfig incompatible image	- reconfiguration image is
						  incompatible with hardware
		* reconfig IP protocol error	- protocol errors detected by
						  reconfiguration hardware
		* reconfig fifo overflow error	- FIFO overflow detected by
						  reconfiguration hardware
