What:		/sys/devices/system/memory
Date:		June 2008
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The /sys/devices/system/memory contains a snapshot of the
		internal state of the kernel memory blocks. Files could be
		added or removed dynamically to represent hot-add/remove
		operations.
Users:		hotplug memory add/remove tools
		http://www.ibm.com/developerworks/wikis/display/LinuxP/powerpc-utils

What:		/sys/devices/system/memory/memoryX/removable
Date:		June 2008
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/removable
		indicates whether this memory block is removable or not.
		This is useful for a user-level agent to determine
		identify removable sections of the memory before attempting
		potentially expensive hot-remove memory operation
Users:		hotplug memory remove tools
		http://www.ibm.com/developerworks/wikis/display/LinuxP/powerpc-utils	

What:		/sys/devices/system/memory/memoryX/phys_device
Date:		September 2008
Contact:	Bad<PERSON> <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/phys_device
		is read-only and is designed to show the name of physical
		memory device.  Implementation is currently incomplete.

What:		/sys/devices/system/memory/memoryX/phys_index
Date:		September 2008
Contact:	Badari Pulavarty <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/phys_index
		is read-only and contains the section ID in hexadecimal
		which is equivalent to decimal X contained in the
		memory section directory name.

What:		/sys/devices/system/memory/memoryX/state
Date:		September 2008
Contact:	Badari Pulavarty <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/state
		is read-write.  When read, its contents show the
		online/offline state of the memory section.  When written,
		root can toggle the the online/offline state of a removable
		memory section (see removable file description above)
		using the following commands.
		# echo online > /sys/devices/system/memory/memoryX/state
		# echo offline > /sys/devices/system/memory/memoryX/state

		For example, if /sys/devices/system/memory/memory22/removable
		contains a value of 1 and
		/sys/devices/system/memory/memory22/state contains the
		string "online" the following command can be executed by
		by root to offline that section.
		# echo offline > /sys/devices/system/memory/memory22/state
Users:		hotplug memory remove tools
		http://www.ibm.com/developerworks/wikis/display/LinuxP/powerpc-utils


What:           /sys/devices/system/memory/memoryX/valid_zones
Date:           July 2014
Contact:	Zhang Zhen <<EMAIL>>
Description:
		The file /sys/devices/system/memory/memoryX/valid_zones	is
		read-only and is designed to show which zone this memory
		block can be onlined to.

What:		/sys/devices/system/memoryX/nodeY
Date:		October 2009
Contact:	Linux Memory Management list <<EMAIL>>
Description:
		When CONFIG_NUMA is enabled, a symbolic link that
		points to the corresponding NUMA node directory.

		For example, the following symbolic link is created for
		memory section 9 on node0:
		/sys/devices/system/memory/memory9/node0 -> ../../node/node0


What:		/sys/devices/system/node/nodeX/memoryY
Date:		September 2008
Contact:	Gary Hade <<EMAIL>>
Description:
		When CONFIG_NUMA is enabled
		/sys/devices/system/node/nodeX/memoryY is a symbolic link that
		points to the corresponding /sys/devices/system/memory/memoryY
		memory section directory.  For example, the following symbolic
		link is created for memory section 9 on node0.
		/sys/devices/system/node/node0/memory9 -> ../../memory/memory9
