What:		/sys/devices/platform/sh_mobile_lcdc_fb.[0-3]/graphics/fb[0-9]/ovl_alpha
Date:		May 2012
Contact:	<PERSON> <<EMAIL>>
Description:
		This file is only available on fb[0-9] devices corresponding
		to overlay planes.

		Stores the alpha blending value for the overlay. Values range
		from 0 (transparent) to 255 (opaque). The value is ignored if
		the mode is not set to Alpha Blending.

What:		/sys/devices/platform/sh_mobile_lcdc_fb.[0-3]/graphics/fb[0-9]/ovl_mode
Date:		May 2012
Contact:	<PERSON> <<EMAIL>>
Description:
		This file is only available on fb[0-9] devices corresponding
		to overlay planes.

		Selects the composition mode for the overlay. Possible values
		are

		0 - Alpha Blending
		1 - ROP3

What:		/sys/devices/platform/sh_mobile_lcdc_fb.[0-3]/graphics/fb[0-9]/ovl_position
Date:		May 2012
Contact:	<PERSON> <<EMAIL>>
Description:
		This file is only available on fb[0-9] devices corresponding
		to overlay planes.

		Stores the x,y overlay position on the display in pixels. The
		position format is `[0-9]+,[0-9]+'.

What:		/sys/devices/platform/sh_mobile_lcdc_fb.[0-3]/graphics/fb[0-9]/ovl_rop3
Date:		May 2012
Contact:	Laurent Pinchart <<EMAIL>>
Description:
		This file is only available on fb[0-9] devices corresponding
		to overlay planes.

		Stores the raster operation (ROP3) for the overlay. Values
		range from 0 to 255. The value is ignored if the mode is not
		set to ROP3.
