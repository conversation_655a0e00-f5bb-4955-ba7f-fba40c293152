What:		/sys/bus/hid/drivers/wiimote/<dev>/led1
What:		/sys/bus/hid/drivers/wiimote/<dev>/led2
What:		/sys/bus/hid/drivers/wiimote/<dev>/led3
What:		/sys/bus/hid/drivers/wiimote/<dev>/led4
Date:		July 2011
KernelVersion:	3.1
Contact:	<PERSON> <<EMAIL>>
Description:	Make it possible to set/get current led state. Reading from it
		returns 0 if led is off and 1 if it is on. Writing 0 to it
		disables the led, writing 1 enables it.

What:		/sys/bus/hid/drivers/wiimote/<dev>/extension
Date:		August 2011
KernelVersion:	3.2
Contact:	<PERSON> <<EMAIL>>
Description:	This file contains the currently connected and initialized
		extensions. It can be one of: none, motionp, nunchuck, classic,
		motionp+nunchuck, motionp+classic
		motionp is the official Nintendo Motion+ extension, nunchuck is
		the official Nintendo Nunchuck extension and classic is the
		Nintendo Classic Controller extension. The motionp extension can
		be combined with the other two.
		Starting with kernel-version 3.11 Motion Plus hotplugging is
		supported and if detected, it's no longer reported as static
		extension. You will get uevent notifications for the motion-plus
		device then.

What:		/sys/bus/hid/drivers/wiimote/<dev>/devtype
Date:		May 2013
KernelVersion:	3.11
Contact:	<PERSON> <<EMAIL>>
Description:	While a device is initialized by the wiimote driver, we perform
		a device detection and signal a "change" uevent after it is
		done. This file shows the detected device type. "pending" means
		that the detection is still ongoing, "unknown" means, that the
		device couldn't be detected or loaded. "generic" means, that the
		device couldn't be detected but supports basic Wii Remote
		features and can be used.
		Other strings for each device-type are available and may be
		added if new device-specific detections are added.
		Currently supported are:
			gen10: First Wii Remote generation
			gen20: Second Wii Remote Plus generation (builtin MP)
			balanceboard: Wii Balance Board

What:		/sys/bus/hid/drivers/wiimote/<dev>/bboard_calib
Date:		May 2013
KernelVersion:	3.11
Contact:	David Herrmann <<EMAIL>>
Description:	This attribute is only provided if the device was detected as a
		balance board. It provides a single line with 3 calibration
		values for all 4 sensors. The values are separated by colons and
		are each 2 bytes long (encoded as 4 digit hexadecimal value).
		First, 0kg values for all 4 sensors are written, followed by the
		17kg values for all 4 sensors and last the 34kg values for all 4
		sensors.
		Calibration data is already applied by the kernel to all input
		values but may be used by user-space to perform other
		transformations.

What:		/sys/bus/hid/drivers/wiimote/<dev>/pro_calib
Date:		October 2013
KernelVersion:	3.13
Contact:	David Herrmann <<EMAIL>>
Description:	This attribute is only provided if the device was detected as a
		pro-controller. It provides a single line with 4 calibration
		values for all 4 analog sticks. Format is: "x1:y1 x2:y2". Data
		is prefixed with a +/-. Each value is a signed 16bit number.
		Data is encoded as decimal numbers and specifies the offsets of
		the analog sticks of the pro-controller.
		Calibration data is already applied by the kernel to all input
		values but may be used by user-space to perform other
		transformations.
		Calibration data is detected by the kernel during device setup.
		You can write "scan\n" into this file to re-trigger calibration.
		You can also write data directly in the form "x1:y1 x2:y2" to
		set the calibration values manually.
