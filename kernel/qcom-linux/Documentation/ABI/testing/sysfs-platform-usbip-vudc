What:		/sys/devices/platform/usbip-vudc.%d/dev_desc
Date:		April 2016
KernelVersion:	4.6
Contact:	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		This file allows to read device descriptor of
		gadget driver which is currently bound to this
		controller. It is possible to read this file
		only if gadget driver is bound, otherwise error
		is returned.

What:		/sys/devices/platform/usbip-vudc.%d/usbip_status
Date:		April 2016
KernelVersion:	4.6
Contact:	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		Current status of the device.
		Allowed values:
		1 - Device is available and can be exported
		2 - Device is currently exported
		3 - Fatal error occurred during communication
		  with peer

What:		/sys/devices/platform/usbip-vudc.%d/usbip_sockfd
Date:		April 2016
KernelVersion:	4.6
Contact:	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		This file allows to export usb device to
		connection peer. It is done by writing to this
		file socket fd (as a string for example "8")
		associated with a connection to remote peer who
		would like to use this device. It is possible to
		close the connection by writing -1 instead of
		socked fd.
