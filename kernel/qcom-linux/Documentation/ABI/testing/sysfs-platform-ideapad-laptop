What:		/sys/devices/platform/ideapad/camera_power
Date:		Dec 2010
KernelVersion:	2.6.37
Contact:	"<PERSON><PERSON> <<EMAIL>>"
Description:
		Control the power of camera module. 1 means on, 0 means off.

What:		/sys/devices/platform/ideapad/fan_mode
Date:		June 2012
KernelVersion:	3.6
Contact:	"Maxim Mikityanskiy <<EMAIL>>"
Description:
		Change fan mode
		There are four available modes:
			* 0 -> Super Silent Mode
			* 1 -> Standard Mode
			* 2 -> Dust Cleaning
			* 4 -> Efficient Thermal Dissipation Mode

What:		/sys/devices/platform/ideapad/touchpad
Date:		May 2017
KernelVersion:	4.13
Contact:	"<PERSON><PERSON> <<EMAIL>>"
Description:
		Control touchpad mode.
			* 1 -> Switched On
			* 0 -> Switched Off

What:		/sys/bus/pci/devices/<bdf>/<device>/VPC2004:00/fn_lock
Date:		May 2018
KernelVersion:	4.18
Contact:	"Ole<PERSON> <<EMAIL>>"
Description:
		Control fn-lock mode.
			* 1 -> Switched On
			* 0 -> Switched Off

		For example:
		# echo "0" >	\
		/sys/bus/pci/devices/0000:00:1f.0/PNP0C09:00/VPC2004:00/fn_lock
