What:		/sys/class/leds/<led>/device_name
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Specifies the network device name to monitor.

What:		/sys/class/leds/<led>/interval
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Specifies the duration of the LED blink in milliseconds.
		Defaults to 50 ms.

What:		/sys/class/leds/<led>/link
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Signal the link state of the named network device.
		If set to 0 (default), the LED's normal state is off.
		If set to 1, the LED's normal state reflects the link state
		of the named network device.
		Setting this value also immediately changes the LED state.

What:		/sys/class/leds/<led>/tx
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Signal transmission of data on the named network device.
		If set to 0 (default), the LED will not blink on transmission.
		If set to 1, the LED will blink for the milliseconds specified
		in interval to signal transmission.

What:		/sys/class/leds/<led>/rx
Date:		Dec 2017
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		Signal reception of data on the named network device.
		If set to 0 (default), the LED will not blink on reception.
		If set to 1, the LED will blink for the milliseconds specified
		in interval to signal reception.
