What:		/sys/class/stm/<stm>/masters
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:
		Shows first and last available to software master numbers on
		this STM device.

What:		/sys/class/stm/<stm>/channels
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:
		Shows the number of channels per master on this STM device.

What:		/sys/class/stm/<stm>/hw_override
Date:		March 2016
KernelVersion:	4.7
Contact:	<PERSON> <<EMAIL>>
Description:
		Reads as 0 if master numbers in the STP stream produced by
		this stm device will match the master numbers assigned by
		the software or 1 if the stm hardware overrides software
		assigned masters.
