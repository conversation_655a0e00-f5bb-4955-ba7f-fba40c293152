What:		/sys/fs/ocfs2/
Date:		April 2008
Contact:	<EMAIL>
Description:
		The /sys/fs/ocfs2 directory contains knobs used by the
		ocfs2-tools to interact with the filesystem.

What:		/sys/fs/ocfs2/max_locking_protocol
Date:		April 2008
Contact:	<EMAIL>
Description:
		The /sys/fs/ocfs2/max_locking_protocol file displays version
		of ocfs2 locking supported by the filesystem.  This version
		covers how ocfs2 uses distributed locking between cluster
		nodes.

		The protocol version has a major and minor number.  Two
		cluster nodes can interoperate if they have an identical
		major number and an overlapping minor number - thus,
		a node with version 1.10 can interoperate with a node
		sporting version 1.8, as long as both use the 1.8 protocol.

		Reading from this file returns a single line, the major
		number and minor number joined by a period, eg "1.10".

		This file is read-only.  The value is compiled into the
		driver.

What:		/sys/fs/ocfs2/loaded_cluster_plugins
Date:		April 2008
Contact:	<EMAIL>
Description:
		The /sys/fs/ocfs2/loaded_cluster_plugins file describes
		the available plugins to support ocfs2 cluster operation.
		A cluster plugin is required to use ocfs2 in a cluster.
		There are currently two available plugins:

		* 'o2cb' - The classic o2cb cluster stack that ocfs2 has
			used since its inception.
		* 'user' - A plugin supporting userspace cluster software
			in conjunction with fs/dlm.

		Reading from this file returns the names of all loaded
		plugins, one per line.

		This file is read-only.  Its contents may change as
		plugins are loaded or removed.

What:		/sys/fs/ocfs2/active_cluster_plugin
Date:		April 2008
Contact:	<EMAIL>
Description:
		The /sys/fs/ocfs2/active_cluster_plugin displays which
		cluster plugin is currently in use by the filesystem.
		The active plugin will appear in the loaded_cluster_plugins
		file as well.  Only one plugin can be used at a time.

		Reading from this file returns the name of the active plugin
		on a single line.

		This file is read-only.  Which plugin is active depends on
		the cluster stack in use.  The contents may change
		when all filesystems are unmounted and the cluster stack
		is changed.

What:		/sys/fs/ocfs2/cluster_stack
Date:		April 2008
Contact:	<EMAIL>
Description:
		The /sys/fs/ocfs2/cluster_stack file contains the name
		of current ocfs2 cluster stack.  This value is set by
		userspace tools when bringing the cluster stack online.

		Cluster stack names are 4 characters in length.

		When the 'o2cb' cluster stack is used, the 'o2cb' cluster
		plugin is active.  All other cluster stacks use the 'user'
		cluster plugin.

		Reading from this file returns the name of the current
		cluster stack on a single line.

		Writing a new stack name to this file changes the current
		cluster stack unless there are mounted ocfs2 filesystems.
		If there are mounted filesystems, attempts to change the
		stack return an error.

Users:
	ocfs2-tools <<EMAIL>>
