What:		/sys/firmware/efi/esrt/
Date:		February 2015
Contact:	<PERSON> <<EMAIL>>
Description:	Provides userland access to read the EFI System Resource Table
		(ESRT), a catalog of firmware for which can be updated with
		the UEFI UpdateCapsule mechanism described in section 7.5 of
		the UEFI Standard.
Users:		fwupdate - https://github.com/rhinstaller/fwupdate

What:		/sys/firmware/efi/esrt/fw_resource_count
Date:		February 2015
Contact:	<PERSON> <<EMAIL>>
Description:	The number of entries in the ESRT

What:		/sys/firmware/efi/esrt/fw_resource_count_max
Date:		February 2015
Contact:	<PERSON> <pjon<PERSON>@redhat.com>
Description:	The maximum number of entries that /could/ be registered
		in the allocation the table is currently in.  This is
		really only useful to the system firmware itself.

What:		/sys/firmware/efi/esrt/fw_resource_version
Date:		February 2015
Contact:	<PERSON> <<EMAIL>>
Description:	The version of the ESRT structure provided by the firmware.

What:		/sys/firmware/efi/esrt/entries/entry$N/
Date:		February 2015
Contact:	<PERSON> <<EMAIL>>
Description:	Each ESRT entry is identified by a GUID, and each gets a
		subdirectory under entries/ .
		example: /sys/firmware/efi/esrt/entries/entry0/

What:		/sys/firmware/efi/esrt/entries/entry$N/fw_type
Date:		February 2015
Contact:	Peter Jones <<EMAIL>>
Description:	What kind of firmware entry this is:
		0 - Unknown
		1 - System Firmware
		2 - Device Firmware
		3 - UEFI Driver

What:		/sys/firmware/efi/esrt/entries/entry$N/fw_class
Date:		February 2015
Contact:	Peter Jones <<EMAIL>>
Description:	This is the entry's guid, and will match the directory name.

What:		/sys/firmware/efi/esrt/entries/entry$N/fw_version
Date:		February 2015
Contact:	Peter Jones <<EMAIL>>
Description:	The version of the firmware currently installed.  This is a
		32-bit unsigned integer.

What:		/sys/firmware/efi/esrt/entries/entry$N/lowest_supported_fw_version
Date:		February 2015
Contact:	Peter Jones <<EMAIL>>
Description:	The lowest version of the firmware that can be installed.

What:		/sys/firmware/efi/esrt/entries/entry$N/capsule_flags
Date:		February 2015
Contact:	Peter Jones <<EMAIL>>
Description:	Flags that must be passed to UpdateCapsule()

What:		/sys/firmware/efi/esrt/entries/entry$N/last_attempt_version
Date:		February 2015
Contact:	Peter Jones <<EMAIL>>
Description:	The last firmware version for which an update was attempted.

What:		/sys/firmware/efi/esrt/entries/entry$N/last_attempt_status
Date:		February 2015
Contact:	Peter Jones <<EMAIL>>
Description:	The result of the last firmware update attempt for the
		firmware resource entry.
		0 - Success
		1 - Insufficient resources
		2 - Incorrect version
		3 - Invalid format
		4 - Authentication error
		5 - AC power event
		6 - Battery power event

