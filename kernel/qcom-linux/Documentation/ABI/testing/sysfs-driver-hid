What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/report_descriptor
What:		/sys/class/bluetooth/hci<addr>/<hid-bus>:<vendor-id>:<product-id>.<num>/report_descriptor
What:		/sys/class/hidraw/hidraw<num>/device/report_descriptor
Date:		Jan 2011
KernelVersion:	2.0.39
Contact:	<PERSON> <<EMAIL>>
Description:	When read, this file returns the device's raw binary HID
		report descriptor.
		This file cannot be written.
Users:		HIDAPI library (http://www.signal11.us/oss/hidapi)

What:		/sys/bus/usb/devices/<busnum>-<devnum>:<config num>.<interface num>/<hid-bus>:<vendor-id>:<product-id>.<num>/country
What:		/sys/class/bluetooth/hci<addr>/<hid-bus>:<vendor-id>:<product-id>.<num>/country
What:		/sys/class/hidraw/hidraw<num>/device/country
Date:		February 2015
KernelVersion:	3.19
Contact:	<PERSON> <<EMAIL>>
Description:	When read, this file returns the hex integer value in ASCII
		of the device's HID country code (e.g. 21 for US).
		This file cannot be written.
