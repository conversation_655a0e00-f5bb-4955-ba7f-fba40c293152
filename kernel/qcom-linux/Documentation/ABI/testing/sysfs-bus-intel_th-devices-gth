What:		/sys/bus/intel_th/devices/<intel_th_id>-gth/masters/*
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	(RW) Configure output ports for STP masters. Writing -1
		disables a master; any

What:		/sys/bus/intel_th/devices/<intel_th_id>-gth/outputs/[0-7]_port
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	(RO) Output port type:
		  0: not present,
		  1: MSU (Memory Storage Unit)
		  2: CTP (Common Trace Port)
		  4: PTI (MIPI PTI).

What:		/sys/bus/intel_th/devices/<intel_th_id>-gth/outputs/[0-7]_drop
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <ale<PERSON>.<EMAIL>>
Description:	(RW) Data retention policy setting: keep (0) or drop (1)
		incoming data while output port is in reset.

What:		/sys/bus/intel_th/devices/<intel_th_id>-gth/outputs/[0-7]_null
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:	(RW) STP NULL packet generation: enabled (1) or disabled (0).

What:		/sys/bus/intel_th/devices/<intel_th_id>-gth/outputs/[0-7]_flush
Date:		June 2015
KernelVersion:	4.3
Contact:	Alexander Shishkin <<EMAIL>>
Description:	(RW) Force flush data from byte packing buffer for the output
		port.

What:		/sys/bus/intel_th/devices/<intel_th_id>-gth/outputs/[0-7]_reset
Date:		June 2015
KernelVersion:	4.3
Contact:	Alexander Shishkin <<EMAIL>>
Description:	(RO) Output port is in reset (1).

What:		/sys/bus/intel_th/devices/<intel_th_id>-gth/outputs/[0-7]_smcfreq
Date:		June 2015
KernelVersion:	4.3
Contact:	Alexander Shishkin <<EMAIL>>
Description:	(RW) STP sync packet frequency for the port. Specifies the
		number of clocks between mainenance packets.
