What:           /sys/class/uwb_rc/uwbN/wusbhc/wusb_chid
Date:           July 2008
KernelVersion:  2.6.27
Contact:        <PERSON> <<EMAIL>>
Description:
                Write the CHID (16 space-separated hex octets) for this host controller.
                This starts the host controller, allowing it to accept connection from
                WUSB devices.

                Set an all zero CHID to stop the host controller.

What:           /sys/class/uwb_rc/uwbN/wusbhc/wusb_trust_timeout
Date:           July 2008
KernelVersion:  2.6.27
Contact:        <PERSON> <<EMAIL>>
Description:
                Devices that haven't sent a WUSB packet to the host
                within 'wusb_trust_timeout' ms are considered to have
                disconnected and are removed.  The default value of
                4000 ms is the value required by the WUSB
                specification.

                Since this relates to security (specifically, the
                lifetime of PTKs and GTKs) it should not be changed
                from the default.

What:           /sys/class/uwb_rc/uwbN/wusbhc/wusb_phy_rate
Date:           August 2009
KernelVersion:  2.6.32
Contact:        <PERSON> <<EMAIL>>
Description:
                The maximum PHY rate to use for all connected devices.
                This is only of limited use for testing and
                development as the hardware's automatic rate
                adaptation is better then this simple control.

                Refer to [ECMA-368] section ******** for the value to
                use.

What:           /sys/class/uwb_rc/uwbN/wusbhc/wusb_dnts
Date:           June 2013
KernelVersion:  3.11
Contact:        Thomas Pugliese <<EMAIL>>
Description:
                The device notification time slot (DNTS) count and inverval in
                milliseconds that the WUSB host should use.  This controls how
                often the devices will have the opportunity to send
                notifications to the host.

What:           /sys/class/uwb_rc/uwbN/wusbhc/wusb_retry_count
Date:           June 2013
KernelVersion:  3.11
Contact:        Thomas Pugliese <<EMAIL>>
Description:
                The number of retries that the WUSB host should attempt
                before reporting an error for a bus transaction.  The range of
                valid values is [0..15], where 0 indicates infinite retries.
