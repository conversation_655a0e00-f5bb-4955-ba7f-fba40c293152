What:		/sys/class/lcd/<lcd>/lcd_power
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<PERSON> <<EMAIL>>
Description:
		Control LCD power, values are FB_BLANK_* from fb.h
		 - FB_BLANK_UNBLANK (0)   : power on.
		 - FB_BLANK_POWERDOWN (4) : power off

What:		/sys/class/lcd/<lcd>/contrast
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<PERSON> <<EMAIL>>
Description:
		Current contrast of this LCD device. Value is between 0 and
		/sys/class/lcd/<lcd>/max_contrast.

What:		/sys/class/lcd/<lcd>/max_contrast
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<PERSON> <rpur<PERSON>@rpsys.net>
Description:
		Maximum contrast for this LCD device.
