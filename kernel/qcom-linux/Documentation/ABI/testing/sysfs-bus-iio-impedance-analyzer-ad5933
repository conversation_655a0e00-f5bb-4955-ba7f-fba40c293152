What:		/sys/bus/iio/devices/iio:deviceX/out_altvoltageY_frequency_start
Date:		March 2019
KernelVersion:	3.1.0
Contact:	<EMAIL>
Description:
		Frequency sweep start frequency in Hz.

What:		/sys/bus/iio/devices/iio:deviceX/out_altvoltageY_frequency_increment
Date:		March 2019
KernelVersion:	3.1.0
Contact:	<EMAIL>
Description:
		Frequency increment in Hz (step size) between consecutive
		frequency points along the sweep.

What:		/sys/bus/iio/devices/iio:deviceX/out_altvoltageY_frequency_points
Date:		March 2019
KernelVersion:	3.1.0
Contact:	<EMAIL>
Description:
		Number of frequency points (steps) in the frequency sweep.
		This value, in conjunction with the
		out_altvoltageY_frequency_start and the
		out_altvoltageY_frequency_increment, determines the frequency
		sweep range for the sweep operation.

What:		/sys/bus/iio/devices/iio:deviceX/out_altvoltageY_settling_cycles
Date:		March 2019
KernelVersion:	3.1.0
Contact:	<EMAIL>
Description:
		Number of output excitation cycles (settling time cycles)
		that are allowed to pass through the unknown impedance,
		after each frequency increment, and before the ADC is triggered
		to perform a conversion sequence of the response signal.
