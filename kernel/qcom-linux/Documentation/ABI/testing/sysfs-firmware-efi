What:		/sys/firmware/efi/fw_vendor
Date:		December 2013
Contact:	<PERSON> <<EMAIL>>
Description:	It shows the physical address of firmware vendor field in the
		EFI system table.
Users:		Kexec

What:		/sys/firmware/efi/runtime
Date:		December 2013
Contact:	<PERSON> <<EMAIL>>
Description:	It shows the physical address of runtime service table entry in
		the EFI system table.
Users:		Kexec

What:		/sys/firmware/efi/config_table
Date:		December 2013
Contact:	<PERSON> <<EMAIL>>
Description:	It shows the physical address of config table entry in the EFI
		system table.
Users:		Kexec

What:		/sys/firmware/efi/systab
Date:		April 2005
Contact:	<EMAIL>
Description:	Displays the physical addresses of all EFI Configuration
		Tables found via the EFI System Table. The order in
		which the tables are printed forms an ABI and newer
		versions are always printed first, i.e. ACPI20 comes
		before ACPI.
Users:		dmidecode

What:		/sys/firmware/efi/tables/rci2
Date:		July 2019
Contact:	Narendra K <<EMAIL>>, <EMAIL>
Description:	Displays the content of the Runtime Configuration Interface
		Table version 2 on Dell EMC PowerEdge systems in binary format
Users:		It is used by Dell EMC OpenManage Server Administrator tool to
		populate BIOS setup page.
