What:		/sys/firmware/sfi/tables/
Date:		May 2010
Contact:	<PERSON> <<EMAIL>>
Description:
		SFI defines a number of small static memory tables
		so the kernel can get platform information from firmware.

		The tables are defined in the latest SFI specification:
		http://simplefirmware.org/documentation

		While the tables are used by the kernel, user-space
		can observe them this way:

		# cd /sys/firmware/sfi/tables
		# cat $TABLENAME > $TABLENAME.bin
