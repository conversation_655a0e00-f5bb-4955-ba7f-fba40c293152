For all of the nmem device attributes under nfit/*, see the 'NVDIMM Firmware
Interface Table (NFIT)' section in the ACPI specification
(http://www.uefi.org/specifications) for more details.

What:		/sys/bus/nd/devices/nmemX/nfit/serial
Date:		Jun, 2015
KernelVersion:	v4.2
Contact:	<EMAIL>
Description:
		(RO) Serial number of the NVDIMM (non-volatile dual in-line
		memory module), assigned by the module vendor.


What:		/sys/bus/nd/devices/nmemX/nfit/handle
Date:		Apr, 2015
KernelVersion:	v4.2
Contact:	<EMAIL>
Description:
		(RO) The address (given by the _ADR object) of the device on its
		parent bus of the NVDIMM device containing the NVDIMM region.


What:		/sys/bus/nd/devices/nmemX/nfit/device
Date:		Apr, 2015
KernelVersion:	v4.1
Contact:	<EMAIL>
Description:
		(RO) Device id for the NVDIMM, assigned by the module vendor.


What:		/sys/bus/nd/devices/nmemX/nfit/rev_id
Date:		Jun, 2015
KernelVersion:	v4.2
Contact:	<EMAIL>
Description:
		(RO) Revision of the NVDIMM, assigned by the module vendor.


What:		/sys/bus/nd/devices/nmemX/nfit/phys_id
Date:		Apr, 2015
KernelVersion:	v4.2
Contact:	<EMAIL>
Description:
		(RO) Handle (i.e., instance number) for the SMBIOS (system
		management BIOS) Memory Device structure describing the NVDIMM
		containing the NVDIMM region.


What:		/sys/bus/nd/devices/nmemX/nfit/flags
Date:		Jun, 2015
KernelVersion:	v4.2
Contact:	<EMAIL>
Description:
		(RO) The flags in the NFIT memory device sub-structure indicate
		the state of the data on the nvdimm relative to its energy
		source or last "flush to persistence".

		The attribute is a translation of the 'NVDIMM State Flags' field
		in section ******** 'NVDIMM Region Mapping' Structure of the
		ACPI specification 6.2.

		The health states are "save_fail", "restore_fail", "flush_fail",
		"not_armed", "smart_event", "map_fail" and "smart_notify".


What:		/sys/bus/nd/devices/nmemX/nfit/format
What:		/sys/bus/nd/devices/nmemX/nfit/format1
What:		/sys/bus/nd/devices/nmemX/nfit/formats
Date:		Apr, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) The interface codes indicate support for persistent memory
		mapped directly into system physical address space and / or a
		block aperture access mechanism to the NVDIMM media.
		The 'formats' attribute displays the number of supported
		interfaces.

		This layout is compatible with existing libndctl binaries that
		only expect one code per-dimm as they will ignore
		nmemX/nfit/formats and nmemX/nfit/formatN.


What:		/sys/bus/nd/devices/nmemX/nfit/vendor
Date:		Apr, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) Vendor id of the NVDIMM.


What:		/sys/bus/nd/devices/nmemX/nfit/dsm_mask
Date:		May, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) The bitmask indicates the supported device specific control
		functions relative to the NVDIMM command family supported by the
		device


What:		/sys/bus/nd/devices/nmemX/nfit/family
Date:		Apr, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) Displays the NVDIMM family command sets. Values
		0, 1, 2 and 3 correspond to NVDIMM_FAMILY_INTEL,
		NVDIMM_FAMILY_HPE1, NVDIMM_FAMILY_HPE2 and NVDIMM_FAMILY_MSFT
		respectively.

		See the specifications for these command families here:
		http://pmem.io/documents/NVDIMM_DSM_Interface-V1.6.pdf
		https://github.com/HewlettPackard/hpe-nvm/blob/master/Documentation/
		https://msdn.microsoft.com/library/windows/hardware/mt604741"


What:		/sys/bus/nd/devices/nmemX/nfit/id
Date:		Apr, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) ACPI specification 6.2 section ********, defines an
		identifier for an NVDIMM, which refelects the id attribute.


What:		/sys/bus/nd/devices/nmemX/nfit/subsystem_vendor
Date:		Apr, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) Sub-system vendor id of the NVDIMM non-volatile memory
		subsystem controller.


What:		/sys/bus/nd/devices/nmemX/nfit/subsystem_rev_id
Date:		Apr, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) Sub-system revision id of the NVDIMM non-volatile memory subsystem
		controller, assigned by the non-volatile memory subsystem
		controller vendor.


What:		/sys/bus/nd/devices/nmemX/nfit/subsystem_device
Date:		Apr, 2016
KernelVersion:	v4.7
Contact:	<EMAIL>
Description:
		(RO) Sub-system device id for the NVDIMM non-volatile memory
		subsystem controller, assigned by the non-volatile memory
		subsystem controller vendor.


What:		/sys/bus/nd/devices/ndbusX/nfit/revision
Date:		Jun, 2015
KernelVersion:	v4.2
Contact:	<EMAIL>
Description:
		(RO) ACPI NFIT table revision number.


What:		/sys/bus/nd/devices/ndbusX/nfit/scrub
Date:		Sep, 2016
KernelVersion:	v4.9
Contact:	<EMAIL>
Description:
		(RW) This shows the number of full Address Range Scrubs (ARS)
		that have been completed since driver load time. Userspace can
		wait on this using select/poll etc. A '+' at the end indicates
		an ARS is in progress

		Writing a value of 1 triggers an ARS scan.


What:		/sys/bus/nd/devices/ndbusX/nfit/hw_error_scrub
Date:		Sep, 2016
KernelVersion:	v4.9
Contact:	<EMAIL>
Description:
		(RW) Provides a way to toggle the behavior between just adding
		the address (cache line) where the MCE happened to the poison
		list and doing a full scrub. The former (selective insertion of
		the address) is done unconditionally.

		This attribute can have the following values written to it:

		'0': Switch to the default mode where an exception will only
		insert the address of the memory error into the poison and
		badblocks lists.
		'1': Enable a full scrub to happen if an exception for a memory
		error is received.


What:		/sys/bus/nd/devices/ndbusX/nfit/dsm_mask
Date:		Jun, 2017
KernelVersion:	v4.13
Contact:	<EMAIL>
Description:
		(RO) The bitmask indicates the supported bus specific control
		functions. See the section named 'NVDIMM Root Device _DSMs' in
		the ACPI specification.


What:		/sys/bus/nd/devices/regionX/nfit/range_index
Date:		Jun, 2015
KernelVersion:	v4.2
Contact:	<EMAIL>
Description:
		(RO) A unique number provided by the BIOS to identify an address
		range. Used by NVDIMM Region Mapping Structure to uniquely refer
		to this structure. Value of 0 is reserved and not used as an
		index.
