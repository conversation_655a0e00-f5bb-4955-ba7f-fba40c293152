sysfs interface for analog devices adp8860 backlight driver
-----------------------------------------------------------

The backlight brightness control operates at three different levels for the
adp8860, adp8861 and adp8863 devices: daylight (level 1), office (level 2) and
dark (level 3). By default the brightness operates at the daylight brightness
level.

What:		/sys/class/backlight/<backlight>/ambient_light_level
Date:		Apr, 2010
KernelVersion:	v2.6.35
Contact:	<PERSON> <<EMAIL>>
Description:
		(RO) 13-bit conversion value for the first light sensor—high
		byte (Bit 12 to Bit 8). The value is updated every 80 ms (when
		the light sensor is enabled).


What:		/sys/class/backlight/<backlight>/ambient_light_zone
Date:		Apr, 2010
KernelVersion:	v2.6.35
Contact:	<PERSON> <<EMAIL>>
Description:
		(RW) Read or write the specific level at which the backlight
		operates. Value "0" enables automatic ambient light sensing, and
		values "1", "2" or "3" set the control to daylight, office or
		dark respectively.


What:		/sys/class/backlight/<backlight>/l1_daylight_max
What:		/sys/class/backlight/<backlight>/l2_office_max
What:		/sys/class/backlight/<backlight>/l3_dark_max
Date:		Apr, 2010
KernelVersion:	v2.6.35
Contact:	Michael Hennerich <<EMAIL>>
Description:
		(RW) Maximum current setting for the backlight when brightness
		is at one of the three levels (daylight, office or dark). This
		is an input code between 0 and 127, which is transformed to a
		value between 0 mA and 30 mA using linear or non-linear
		algorithms.


What:		/sys/class/backlight/<backlight>/l1_daylight_dim
What:		/sys/class/backlight/<backlight>/l2_office_dim
What:		/sys/class/backlight/<backlight>/l3_dark_dim
Date:		Apr, 2010
KernelVersion:	v2.6.35
Contact:	Michael Hennerich <<EMAIL>>
Description:
		(RW) Dim current setting for the backlight when brightness is at
		one of the three levels (daylight, office or dark). This is an
		input code between 0 and 127, which is transformed to a value
		between 0 mA and 30 mA using linear or non-linear algorithms.
