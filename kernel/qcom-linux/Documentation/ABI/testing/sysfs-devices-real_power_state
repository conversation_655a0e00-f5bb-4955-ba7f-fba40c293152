What:		/sys/devices/.../real_power_state
Date:		January 2013
Contact:	<PERSON> <<EMAIL>>
Description:
		The /sys/devices/.../real_power_state attribute is only present
		for device objects representing ACPI device nodes that provide
		power management methods and use ACPI power resources for power
		management.

		If present, it contains a string representing the real ACPI
		power state of the given device node as returned by the _PSC
		control method or inferred from the configuration of power
		resources.  Its possible values, "D0", "D1", "D2", "D3hot", and
		"D3cold", reflect the power state names defined by the ACPI
		specification (ACPI 4 and above).

		In some situations the value of this attribute may be different
		from the value of the /sys/devices/.../power_state attribute for
		the same device object.  If that happens, some shared power
		resources used by the device node are only ON because of some
		other devices using them at the moment.

		This attribute is read-only.
