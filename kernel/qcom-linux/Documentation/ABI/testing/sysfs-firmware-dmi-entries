What:		/sys/firmware/dmi/entries/
Date:		February 2011
Contact:	<PERSON> <<EMAIL>>
Description:
		Many machines' firmware (x86 and ia64) export DMI /
		SMBIOS tables to the operating system.  Getting at this
		information is often valuable to userland, especially in
		cases where there are OEM extensions used.

		The kernel itself does not rely on the majority of the
		information in these tables being correct.  It equally
		cannot ensure that the data as exported to userland is
		without error either.

		DMI is structured as a large table of entries, where
		each entry has a common header indicating the type and
		length of the entry, as well as a firmware-provided
		'handle' that is supposed to be unique amongst all
		entries.

		Some entries are required by the specification, but many
		others are optional.  In general though, users should
		never expect to find a specific entry type on their
		system unless they know for certain what their firmware
		is doing.  Machine to machine experiences will vary.

		Multiple entries of the same type are allowed.  In order
		to handle these duplicate entry types, each entry is
		assigned by the operating system an 'instance', which is
		derived from an entry type's ordinal position.  That is
		to say, if there are 'N' multiple entries with the same type
		'T' in the DMI tables (adjacent or spread apart, it
		doesn't matter), they will be represented in sysfs as
		entries "T-0" through "T-(N-1)":

		Example entry directories:

			/sys/firmware/dmi/entries/17-0
			/sys/firmware/dmi/entries/17-1
			/sys/firmware/dmi/entries/17-2
			/sys/firmware/dmi/entries/17-3
			...

		Instance numbers are used in lieu of the firmware
		assigned entry handles as the kernel itself makes no
		guarantees that handles as exported are unique, and
		there are likely firmware images that get this wrong in
		the wild.

		Each DMI entry in sysfs has the common header values
		exported as attributes:

		handle	: The 16bit 'handle' that is assigned to this
			  entry by the firmware.  This handle may be
			  referred to by other entries.
		length	: The length of the entry, as presented in the
			  entry itself.  Note that this is _not the
			  total count of bytes associated with the
			  entry_.  This value represents the length of
			  the "formatted" portion of the entry.  This
			  "formatted" region is sometimes followed by
			  the "unformatted" region composed of nul
			  terminated strings, with termination signalled
			  by a two nul characters in series.
		raw	: The raw bytes of the entry. This includes the
			  "formatted" portion of the entry, the
			  "unformatted" strings portion of the entry,
			  and the two terminating nul characters.
		type	: The type of the entry.  This value is the same
			  as found in the directory name.  It indicates
			  how the rest of the entry should be interpreted.
		instance: The instance ordinal of the entry for the
			  given type.  This value is the same as found
			  in the parent directory name.
		position: The ordinal position (zero-based) of the entry
			  within the entirety of the DMI entry table.

		=== Entry Specialization ===

		Some entry types may have other information available in
		sysfs.  Not all types are specialized.

		--- Type 15 - System Event Log ---

		This entry allows the firmware to export a log of
		events the system has taken.  This information is
		typically backed by nvram, but the implementation
		details are abstracted by this table.  This entry's data
		is exported in the directory:

		/sys/firmware/dmi/entries/15-0/system_event_log

		and has the following attributes (documented in the
		SMBIOS / DMI specification under "System Event Log (Type 15)":

		area_length
		header_start_offset
		data_start_offset
		access_method
		status
		change_token
		access_method_address
		header_format
		per_log_type_descriptor_length
		type_descriptors_supported_count

		As well, the kernel exports the binary attribute:

		raw_event_log	: The raw binary bits of the event log
				  as described by the DMI entry.
