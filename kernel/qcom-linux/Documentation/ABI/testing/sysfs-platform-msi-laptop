What:		/sys/devices/platform/msi-laptop-pf/lcd_level
Date:		Oct 2006
KernelVersion:	2.6.19
Contact:	"Lennart Poettering <<EMAIL>>"
Description:
		Screen brightness: contains a single integer in the range 0..8.

What:		/sys/devices/platform/msi-laptop-pf/auto_brightness
Date:		Oct 2006
KernelVersion:	2.6.19
Contact:	"Lennart Poettering <<EMAIL>>"
Description:
		Enable automatic brightness control: contains either 0 or 1. If
		set to 1 the hardware adjusts the screen brightness
		automatically when the power cord is plugged/unplugged.

What:		/sys/devices/platform/msi-laptop-pf/wlan
Date:		Oct 2006
KernelVersion:	2.6.19
Contact:	"Lennart Poettering <<EMAIL>>"
Description:
		WLAN subsystem enabled: contains either 0 or 1.

What:		/sys/devices/platform/msi-laptop-pf/bluetooth
Date:		Oct 2006
KernelVersion:	2.6.19
Contact:	"Lennart Poettering <<EMAIL>>"
Description:
		Bluetooth subsystem enabled: contains either 0 or 1. Please
		note that this file is constantly 0 if no Bluetooth hardware is
		available.

What:		/sys/devices/platform/msi-laptop-pf/touchpad
Date:		Nov 2012
KernelVersion:	3.8
Contact:	"Maxim Mikityanskiy <<EMAIL>>"
Description:
		Contains either 0 or 1 and indicates if touchpad is turned on.
		Touchpad state can only be toggled by pressing Fn+F3.

What:		/sys/devices/platform/msi-laptop-pf/turbo_mode
Date:		Nov 2012
KernelVersion:	3.8
Contact:	"Maxim Mikityanskiy <<EMAIL>>"
Description:
		Contains either 0 or 1 and indicates if turbo mode is turned
		on. In turbo mode power LED is orange and processor is
		overclocked. Turbo mode is available only if charging. It is
		only possible to toggle turbo mode state by pressing Fn+F10,
		and there is a few seconds cooldown between subsequent toggles.
		If user presses Fn+F10 too frequent, turbo mode state is not
		changed.

What:		/sys/devices/platform/msi-laptop-pf/eco_mode
Date:		Nov 2012
KernelVersion:	3.8
Contact:	"Maxim Mikityanskiy <<EMAIL>>"
Description:
		Contains either 0 or 1 and indicates if ECO mode is turned on.
		In ECO mode power LED is green and userspace should do some
		powersaving actions. ECO mode is available only on battery
		power. ECO mode can only be toggled by pressing Fn+F10.

What:		/sys/devices/platform/msi-laptop-pf/turbo_cooldown
Date:		Nov 2012
KernelVersion:	3.8
Contact:	"Maxim Mikityanskiy <<EMAIL>>"
Description:
		Contains value in range 0..3:
			* 0 -> Turbo mode is off
			* 1 -> Turbo mode is on, cannot be turned off yet
			* 2 -> Turbo mode is off, cannot be turned on yet
			* 3 -> Turbo mode is on

What:		/sys/devices/platform/msi-laptop-pf/auto_fan
Date:		Nov 2012
KernelVersion:	3.8
Contact:	"Maxim Mikityanskiy <<EMAIL>>"
Description:
		Contains either 0 or 1 and indicates if fan speed is controlled
		automatically (1) or fan runs at maximal speed (0). Can be
		toggled in software.

