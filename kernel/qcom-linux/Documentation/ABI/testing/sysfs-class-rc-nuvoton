What:		/sys/class/rc/rcN/wakeup_data
Date:		Mar 2016
KernelVersion:	4.6
Contact:	<PERSON><PERSON> <<EMAIL>>
Description:
		Reading this file returns the stored CIR wakeup sequence.
		It starts with a pulse, followed by a space, pulse etc.
		All values are in microseconds.
		The same format can be used to store a wakeup sequence
		in the Nuvoton chip by writing to this file.

		Note: Some systems reset the stored wakeup sequence to a
		factory default on each boot. On such systems store the
		wakeup sequence in a file and set it on boot using e.g.
		a udev rule.
