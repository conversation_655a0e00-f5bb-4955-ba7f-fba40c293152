What:		/sys/bus/iio/devices/device[n]/in_illuminance_calibrate
KernelVersion:	2.6.37
Contact:	<EMAIL>
Description:
		This property causes an internal calibration of the als gain trim
		value which is later used in calculating illuminance in lux.

What:		/sys/bus/iio/devices/device[n]/in_illuminance_lux_table
KernelVersion:	2.6.37
Contact:	<EMAIL>
Description:
		This property gets/sets the table of coefficients
		used in calculating illuminance in lux.

What:		/sys/bus/iio/devices/device[n]/in_illuminance_input_target
KernelVersion:	2.6.37
Contact:	<EMAIL>
Description:
		This property is the known externally illuminance (in lux).
		It is used in the process of calibrating the device accuracy.
