What:           /sys/module/xen_blkback/parameters/max_buffer_pages
Date:           March 2013
KernelVersion:  3.11
Contact:        <PERSON> <<EMAIL>>
Description:
                Maximum number of free pages to keep in each block
                backend buffer.

What:           /sys/module/xen_blkback/parameters/max_persistent_grants
Date:           March 2013
KernelVersion:  3.11
Contact:        <PERSON> <<EMAIL>>
Description:
                Maximum number of grants to map persistently in
                blkback. If the frontend tries to use more than
                max_persistent_grants, the LRU kicks in and starts
                removing 5% of max_persistent_grants every 100ms.

What:           /sys/module/xen_blkback/parameters/persistent_grant_unused_seconds
Date:           August 2018
KernelVersion:  4.19
Contact:        <PERSON> <<EMAIL>>
Description:
                How long a persistent grant is allowed to remain
                allocated without being in use. The time is in
                seconds, 0 means indefinitely long.
                The default is 60 seconds.
