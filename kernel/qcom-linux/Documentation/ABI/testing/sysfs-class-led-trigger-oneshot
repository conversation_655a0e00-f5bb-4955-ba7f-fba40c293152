What:		/sys/class/leds/<led>/delay_on
Date:		Jun 2012
KernelVersion:	3.6
Contact:	<EMAIL>
Description:
		Specifies for how many milliseconds the LED has to stay at
		LED_FULL brightness after it has been armed.
		Defaults to 100 ms.

What:		/sys/class/leds/<led>/delay_off
Date:		Jun 2012
KernelVersion:	3.6
Contact:	<EMAIL>
Description:
		Specifies for how many milliseconds the LED has to stay at
		LED_OFF brightness after it has been armed.
		Defaults to 100 ms.

What:		/sys/class/leds/<led>/invert
Date:		Jun 2012
KernelVersion:	3.6
Contact:	<EMAIL>
Description:
		Reverse the blink logic. If set to 0 (default) blink on for
		delay_on ms, then blink off for delay_off ms, leaving the LED
		normally off. If set to 1, blink off for delay_off ms, then
		blink on for delay_on ms, leaving the LED normally on.
		Setting this value also immediately changes the LED state.

What:		/sys/class/leds/<led>/shot
Date:		Jun 2012
KernelVersion:	3.6
Contact:	<EMAIL>
Description:
		Write any non-empty string to signal an events, this starts a
		blink sequence if not already running.
