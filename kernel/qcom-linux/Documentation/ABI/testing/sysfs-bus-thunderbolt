What: /sys/bus/thunderbolt/devices/.../domainX/boot_acl
Date:		Jun 2018
KernelVersion:	4.17
Contact:	<EMAIL>
Description:	Holds a comma separated list of device unique_ids that
		are allowed to be connected automatically during system
		startup (e.g boot devices). The list always contains
		maximum supported number of unique_ids where unused
		entries are empty. This allows the userspace software
		to determine how many entries the controller supports.
		If there are multiple controllers, each controller has
		its own ACL list and size may be different between the
		controllers.

		System BIOS may have an option "Preboot ACL" or similar
		that needs to be selected before this list is taken into
		consideration.

		Software always updates a full list in each write.

		If a device is authorized automatically during boot its
		boot attribute is set to 1.

What: /sys/bus/thunderbolt/devices/.../domainX/iommu_dma_protection
Date:		Mar 2019
KernelVersion:	4.21
Contact:	<EMAIL>
Description:	This attribute tells whether the system uses IOMMU
		for DMA protection. Value of 1 means IOMMU is used 0 means
		it is not (DMA protection is solely based on Thunderbolt
		security levels).

What: /sys/bus/thunderbolt/devices/.../domainX/security
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	This attribute holds current Thunderbolt security level
		set by the system BIOS. Possible values are:

		none: All devices are automatically authorized
		user: Devices are only authorized based on writing
		      appropriate value to the authorized attribute
		secure: Require devices that support secure connect at
			minimum. User needs to authorize each device.
		dponly: Automatically tunnel Display port (and USB). No
			PCIe tunnels are created.
		usbonly: Automatically tunnel USB controller of the
			 connected Thunderbolt dock (and Display Port). All
			 PCIe links downstream of the dock are removed.

What: /sys/bus/thunderbolt/devices/.../authorized
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	This attribute is used to authorize Thunderbolt devices
		after they have been connected. If the device is not
		authorized, no devices such as PCIe and Display port are
		available to the system.

		Contents of this attribute will be 0 when the device is not
		yet authorized.

		Possible values are supported:
		1: The device will be authorized and connected

		When key attribute contains 32 byte hex string the possible
		values are:
		1: The 32 byte hex string is added to the device NVM and
		   the device is authorized.
		2: Send a challenge based on the 32 byte hex string. If the
		   challenge response from device is valid, the device is
		   authorized. In case of failure errno will be ENOKEY if
		   the device did not contain a key at all, and
		   EKEYREJECTED if the challenge response did not match.

What: /sys/bus/thunderbolt/devices/.../boot
Date:		Jun 2018
KernelVersion:	4.17
Contact:	<EMAIL>
Description:	This attribute contains 1 if Thunderbolt device was already
		authorized on boot and 0 otherwise.

What: /sys/bus/thunderbolt/devices/.../key
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	When a devices supports Thunderbolt secure connect it will
		have this attribute. Writing 32 byte hex string changes
		authorization to use the secure connection method instead.
		Writing an empty string clears the key and regular connection
		method can be used again.

What:		/sys/bus/thunderbolt/devices/.../device
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	This attribute contains id of this device extracted from
		the device DROM.

What:		/sys/bus/thunderbolt/devices/.../device_name
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	This attribute contains name of this device extracted from
		the device DROM.

What:		/sys/bus/thunderbolt/devices/.../vendor
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	This attribute contains vendor id of this device extracted
		from the device DROM.

What:		/sys/bus/thunderbolt/devices/.../vendor_name
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	This attribute contains vendor name of this device extracted
		from the device DROM.

What:		/sys/bus/thunderbolt/devices/.../unique_id
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	This attribute contains unique_id string of this device.
		This is either read from hardware registers (UUID on
		newer hardware) or based on UID from the device DROM.
		Can be used to uniquely identify particular device.

What:		/sys/bus/thunderbolt/devices/.../nvm_version
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	If the device has upgradeable firmware the version
		number is available here. Format: %x.%x, major.minor.
		If the device is in safe mode reading the file returns
		-ENODATA instead as the NVM version is not available.

What:		/sys/bus/thunderbolt/devices/.../nvm_authenticate
Date:		Sep 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:	When new NVM image is written to the non-active NVM
		area (through non_activeX NVMem device), the
		authentication procedure is started by writing 1 to
		this file. If everything goes well, the device is
		restarted with the new NVM firmware. If the image
		verification fails an error code is returned instead.

		When read holds status of the last authentication
		operation if an error occurred during the process. This
		is directly the status value from the DMA configuration
		based mailbox before the device is power cycled. Writing
		0 here clears the status.

What:		/sys/bus/thunderbolt/devices/<xdomain>.<service>/key
Date:		Jan 2018
KernelVersion:	4.15
Contact:	<EMAIL>
Description:	This contains name of the property directory the XDomain
		service exposes. This entry describes the protocol in
		question. Following directories are already reserved by
		the Apple XDomain specification:

		network:  IP/ethernet over Thunderbolt
		targetdm: Target disk mode protocol over Thunderbolt
		extdisp:  External display mode protocol over Thunderbolt

What:		/sys/bus/thunderbolt/devices/<xdomain>.<service>/modalias
Date:		Jan 2018
KernelVersion:	4.15
Contact:	<EMAIL>
Description:	Stores the same MODALIAS value emitted by uevent for
		the XDomain service. Format: tbtsvc:kSpNvNrN

What:		/sys/bus/thunderbolt/devices/<xdomain>.<service>/prtcid
Date:		Jan 2018
KernelVersion:	4.15
Contact:	<EMAIL>
Description:	This contains XDomain protocol identifier the XDomain
		service supports.

What:		/sys/bus/thunderbolt/devices/<xdomain>.<service>/prtcvers
Date:		Jan 2018
KernelVersion:	4.15
Contact:	<EMAIL>
Description:	This contains XDomain protocol version the XDomain
		service supports.

What:		/sys/bus/thunderbolt/devices/<xdomain>.<service>/prtcrevs
Date:		Jan 2018
KernelVersion:	4.15
Contact:	<EMAIL>
Description:	This contains XDomain software version the XDomain
		service supports.

What:		/sys/bus/thunderbolt/devices/<xdomain>.<service>/prtcstns
Date:		Jan 2018
KernelVersion:	4.15
Contact:	<EMAIL>
Description:	This contains XDomain service specific settings as
		bitmask. Format: %x
