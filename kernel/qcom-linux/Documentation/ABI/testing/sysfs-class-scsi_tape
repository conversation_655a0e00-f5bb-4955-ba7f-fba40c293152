What:           /sys/class/scsi_tape/*/stats/in_flight
Date:           Apr 2015
KernelVersion:  4.2
Contact:        <PERSON> <<EMAIL>>
Description:
		Show the number of I/Os currently in-flight between the st
		module and the SCSI mid-layer.
Users:


What:           /sys/class/scsi_tape/*/stats/io_ns
Date:           Apr 2015
KernelVersion:  4.2
Contact:        <PERSON> <<EMAIL>>
Description:
		Shows the total amount of time spent waiting for all I/O
                to and from the tape drive to complete. This includes all
                reads, writes, and other SCSI commands issued to the tape
                drive. An example of other SCSI commands would be tape
                movement such as a rewind when a rewind tape device is
                closed. This item is measured in nanoseconds.

                To determine the amount of time spent waiting for other I/O
                to complete subtract read_ns and write_ns from this value.
Users:


What:           /sys/class/scsi_tape/*/stats/other_cnt
Date:           Apr 2015
KernelVersion:  4.2
Contact:        <PERSON> <<EMAIL>>
Description:
		The number of I/O requests issued to the tape drive other
		than SCSI read/write requests.
Users:


What:           /sys/class/scsi_tape/*/stats/read_byte_cnt
Date:           Apr 2015
KernelVersion:  4.2
Contact:        <PERSON> <<EMAIL>>
Description:
		Shows the total number of bytes requested from the tape drive.
		This value is presented in bytes because tape drives support
		variable length block sizes.
Users:


What:           /sys/class/scsi_tape/*/stats/read_cnt
Date:           Apr 2015
KernelVersion:  4.2
Contact:        Shane Seymour <<EMAIL>>
Description:
		Shows the total number of read requests issued to the tape
		drive.
Users:


What:           /sys/class/scsi_tape/*/stats/read_ns
Date:           Apr 2015
KernelVersion:  4.2
Contact:        Shane Seymour <<EMAIL>>
Description:
		Shows the total amount of time in nanoseconds waiting for
		read I/O requests to complete.
Users:


What:           /sys/class/scsi_tape/*/stats/write_byte_cnt
Date:           Apr 2015
KernelVersion:  4.2
Contact:        Shane Seymour <<EMAIL>>
Description:
		Shows the total number of bytes written to the tape drive.
		This value is presented in bytes because tape drives support
		variable length block sizes.
Users:


What:           /sys/class/scsi_tape/*/stats/write_cnt
Date:           Apr 2015
KernelVersion:  4.2
Contact:        Shane Seymour <<EMAIL>>
Description:
		Shows the total number of write requests issued to the tape
		drive.
Users:


What:           /sys/class/scsi_tape/*/stats/write_ms
Date:           Apr 2015
KernelVersion:  4.2
Contact:        Shane Seymour <<EMAIL>>
Description:
		Shows the total amount of time in nanoseconds waiting for
		write I/O requests to complete.
Users:


What:           /sys/class/scsi_tape/*/stats/resid_cnt
Date:           Apr 2015
KernelVersion:  4.2
Contact:        Shane Seymour <<EMAIL>>
Description:
		Shows the number of times we found that a residual >0
		was found when the SCSI midlayer indicated that there was
		an error. For reads this may be a case of someone issuing
		reads greater than the block size.
Users:
