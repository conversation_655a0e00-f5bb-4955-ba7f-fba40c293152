What:		/sys/class/net/<iface>/name_assign_type
Date:		July 2014
KernelVersion:	3.17
Contact:	<EMAIL>
Description:
		Indicates the name assignment type. Possible values are:
		1: enumerated by the kernel, possibly in an unpredictable way
		2: predictably named by the kernel
		3: named by userspace
		4: renamed

What:		/sys/class/net/<iface>/addr_assign_type
Date:		July 2010
KernelVersion:	3.2
Contact:	<EMAIL>
Description:
		Indicates the address assignment type. Possible values are:
		0: permanent address
		1: randomly generated
		2: stolen from another device
		3: set using dev_set_mac_address

What:		/sys/class/net/<iface>/addr_len
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the hardware address size in bytes.
		Values vary based on the lower-level protocol used by the
		interface (Ethernet, FDDI, ATM, IEEE 802.15.4...). See
		include/uapi/linux/if_*.h for actual values.

What:		/sys/class/net/<iface>/address
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Hardware address currently assigned to this interface.
		Format is a string, e.g: 00:11:22:33:44:55 for an Ethernet MAC
		address.

What:		/sys/class/net/<bridge iface>/bridge/group_fwd_mask
Date:		January 2012
KernelVersion:	3.2
Contact:	<EMAIL>
Description:
		Bitmask to allow forwarding of link local frames with address
		01-80-C2-00-00-0X on a bridge device. Only values that set bits
		not matching BR_GROUPFWD_RESTRICTED in net/bridge/br_private.h
		allowed.
		Default value 0 does not forward any link local frames.

		Restricted bits:
		0: 01-80-C2-00-00-00 Bridge Group Address used for STP
		1: 01-80-C2-00-00-01 (MAC Control) 802.3 used for MAC PAUSE
		2: 01-80-C2-00-00-02 (Link Aggregation) 802.3ad

		Any values not setting these bits can be used. Take special
		care when forwarding control frames e.g. 802.1X-PAE or LLDP.

What:		/sys/class/net/<iface>/broadcast
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Hardware broadcast address for this interface. Format is a
		string, e.g: ff:ff:ff:ff:ff:ff for an Ethernet broadcast MAC
		address.

What:		/sys/class/net/<iface>/carrier
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the current physical link state of the interface.
		Posssible values are:
		0: physical link is down
		1: physical link is up

		Note: some special devices, e.g: bonding and team drivers will
		allow this attribute to be written to force a link state for
		operating correctly and designating another fallback interface.

What:		/sys/class/net/<iface>/dev_id
Date:		April 2008
KernelVersion:	2.6.26
Contact:	<EMAIL>
Description:
		Indicates the device unique identifier. Format is an hexadecimal
		value. This is used to disambiguate interfaces which might be
		stacked (e.g: VLAN interfaces) but still have the same MAC
		address as their parent device.

What:		/sys/class/net/<iface>/dev_port
Date:		February 2014
KernelVersion:	3.15
Contact:	<EMAIL>
Description:
		Indicates the port number of this network device, formatted
		as a decimal value. Some NICs have multiple independent ports
		on the same PCI bus, device and function. This attribute allows
		userspace to distinguish the respective interfaces.

		Note: some device drivers started to use 'dev_id' for this
		purpose since long before 3.15 and have not adopted the new
		attribute ever since. To query the port number, some tools look
		exclusively at 'dev_port', while others only consult 'dev_id'.
		If a network device has multiple client adapter ports as
		described in the previous paragraph and does not set this
		attribute to its port number, it's a kernel bug.

What:		/sys/class/net/<iface>/dormant
Date:		March 2006
KernelVersion:	2.6.17
Contact:	<EMAIL>
Description:
		Indicates whether the interface is in dormant state. Possible
		values are:
		0: interface is not dormant
		1: interface is dormant

		This attribute can be used by supplicant software to signal that
		the device is not usable unless some supplicant-based
		authentication is performed (e.g: 802.1x). 'link_mode' attribute
		will also reflect the dormant state.

What:		/sys/clas/net/<iface>/duplex
Date:		October 2009
KernelVersion:	2.6.33
Contact:	<EMAIL>
Description:
		Indicates the interface latest or current duplex value. Possible
		values are:
		half: half duplex
		full: full duplex

		Note: This attribute is only valid for interfaces that implement
		the ethtool get_link_ksettings method (mostly Ethernet).

What:		/sys/class/net/<iface>/flags
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the interface flags as a bitmask in hexadecimal. See
		include/uapi/linux/if.h for a list of all possible values and
		the flags semantics.

What:		/sys/class/net/<iface>/ifalias
Date:		September 2008
KernelVersion:	2.6.28
Contact:	<EMAIL>
Description:
		Indicates/stores an interface alias name as a string. This can
		be used for system management purposes.

What:		/sys/class/net/<iface>/ifindex
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the system-wide interface unique index identifier as a
		decimal number. This attribute is used for mapping an interface
		identifier to an interface name. It is used throughout the
		networking stack for specifying the interface specific
		requests/events.

What:		/sys/class/net/<iface>/iflink
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the system-wide interface unique index identifier a
		the interface is linked to. Format is decimal. This attribute is
		used to resolve interfaces chaining, linking and stacking.
		Physical interfaces have the same 'ifindex' and 'iflink' values.

What:		/sys/class/net/<iface>/link_mode
Date:		March 2006
KernelVersion:	2.6.17
Contact:	<EMAIL>
Description:
		Indicates the interface link mode, as a decimal number. This
		attribute should be used in conjunction with 'dormant' attribute
		to determine the interface usability. Possible values:
		0: default link mode
		1: dormant link mode

What:		/sys/class/net/<iface>/mtu
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the interface currently configured MTU value, in
		bytes, and in decimal format. Specific values depends on the
		lower-level interface protocol used. Ethernet devices will show
		a 'mtu' attribute value of 1500 unless changed.

What:		/sys/class/net/<iface>/netdev_group
Date:		January 2011
KernelVersion:	2.6.39
Contact:	<EMAIL>
Description:
		Indicates the interface network device group, as a decimal
		integer. Default value is 0 which corresponds to the initial
		network devices group. The group can be changed to affect
		routing decisions (see: net/ipv4/fib_rules and
		net/ipv6/fib6_rules.c).

What:		/sys/class/net/<iface>/operstate
Date:		March 2006
KernelVersion:	2.6.17
Contact:	<EMAIL>
Description:
		Indicates the interface RFC2863 operational state as a string.
		Possible values are:
		"unknown", "notpresent", "down", "lowerlayerdown", "testing",
		"dormant", "up".

What:		/sys/class/net/<iface>/phys_port_id
Date:		July 2013
KernelVersion:	3.12
Contact:	<EMAIL>
Description:
		Indicates the interface unique physical port identifier within
		the NIC, as a string.

What:		/sys/class/net/<iface>/phys_port_name
Date:		March 2015
KernelVersion:	4.0
Contact:	<EMAIL>
Description:
		Indicates the interface physical port name within the NIC,
		as a string.

What:		/sys/class/net/<iface>/speed
Date:		October 2009
KernelVersion:	2.6.33
Contact:	<EMAIL>
Description:
		Indicates the interface latest or current speed value. Value is
		an integer representing the link speed in Mbits/sec.

		Note: this attribute is only valid for interfaces that implement
		the ethtool get_link_ksettings method (mostly Ethernet).

What:		/sys/class/net/<iface>/tx_queue_len
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the interface transmit queue len in number of packets,
		as an integer value. Value depend on the type of interface,
		Ethernet network adapters have a default value of 1000 unless
		configured otherwise

What:		/sys/class/net/<iface>/type
Date:		April 2005
KernelVersion:	2.6.12
Contact:	<EMAIL>
Description:
		Indicates the interface protocol type as a decimal value. See
		include/uapi/linux/if_arp.h for all possible values.

What:		/sys/class/net/<iface>/phys_switch_id
Date:		November 2014
KernelVersion:	3.19
Contact:	<EMAIL>
Description:
		Indicates the unique physical switch identifier of a switch this
		port belongs to, as a string.

What:		/sys/class/net/<iface>/phydev
Date:		May 2017
KernelVersion:	4.13
Contact:	<EMAIL>
Description:
		Symbolic link to the PHY device this network device is attached
		to.

What:		/sys/class/net/<iface>/carrier_changes
Date:		Mar 2014
KernelVersion:	3.15
Contact:	<EMAIL>
Description:
		32-bit unsigned integer counting the number of times the link has
		seen a change from UP to DOWN and vice versa

What:		/sys/class/net/<iface>/carrier_up_count
Date:		Jan 2018
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		32-bit unsigned integer counting the number of times the link has
		been up

What:		/sys/class/net/<iface>/carrier_down_count
Date:		Jan 2018
KernelVersion:	4.16
Contact:	<EMAIL>
Description:
		32-bit unsigned integer counting the number of times the link has
		been down
