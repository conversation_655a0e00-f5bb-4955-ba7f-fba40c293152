What:		/sys/class/<iface>/queues/rx-<queue>/rps_cpus
Date:		March 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		Mask of the CPU(s) currently enabled to participate into the
		Receive Packet Steering packet processing flow for this
		network device queue. Possible values depend on the number
		of available CPU(s) in the system.

What:		/sys/class/<iface>/queues/rx-<queue>/rps_flow_cnt
Date:		April 2010
KernelVersion:	2.6.35
Contact:	<EMAIL>
Description:
		Number of Receive Packet Steering flows being currently
		processed by this particular network device receive queue.

What:		/sys/class/<iface>/queues/tx-<queue>/tx_timeout
Date:		November 2011
KernelVersion:	3.3
Contact:	<EMAIL>
Description:
		Indicates the number of transmit timeout events seen by this
		network interface transmit queue.

What:		/sys/class/<iface>/queues/tx-<queue>/tx_maxrate
Date:		March 2015
KernelVersion:	4.1
Contact:	<EMAIL>
Description:
		A Mbps max-rate set for the queue, a value of zero means disabled,
		default is disabled.

What:		/sys/class/<iface>/queues/tx-<queue>/xps_cpus
Date:		November 2010
KernelVersion:	2.6.38
Contact:	<EMAIL>
Description:
		Mask of the CPU(s) currently enabled to participate into the
		Transmit Packet Steering packet processing flow for this
		network device transmit queue. Possible vaules depend on the
		number of available CPU(s) in the system.

What:		/sys/class/<iface>/queues/tx-<queue>/xps_rxqs
Date:		June 2018
KernelVersion:	4.18.0
Contact:	<EMAIL>
Description:
		Mask of the receive queue(s) currently enabled to participate
		into the Transmit Packet Steering packet processing flow for this
		network device transmit queue. Possible values depend on the
		number of available receive queue(s) in the network device.
		Default is disabled.

What:		/sys/class/<iface>/queues/tx-<queue>/byte_queue_limits/hold_time
Date:		November 2011
KernelVersion:	3.3
Contact:	<EMAIL>
Description:
		Indicates the hold time in milliseconds to measure the slack
		of this particular network device transmit queue.
		Default value is 1000.

What:		/sys/class/<iface>/queues/tx-<queue>/byte_queue_limits/inflight
Date:		November 2011
KernelVersion:	3.3
Contact:	<EMAIL>
Description:
		Indicates the number of bytes (objects) in flight on this
		network device transmit queue.

What:		/sys/class/<iface>/queues/tx-<queue>/byte_queue_limits/limit
Date:		November 2011
KernelVersion:	3.3
Contact:	<EMAIL>
Description:
		Indicates the current limit of bytes allowed to be queued
		on this network device transmit queue. This value is clamped
		to be within the bounds defined by limit_max and limit_min.

What:		/sys/class/<iface>/queues/tx-<queue>/byte_queue_limits/limit_max
Date:		November 2011
KernelVersion:	3.3
Contact:	<EMAIL>
Description:
		Indicates the absolute maximum limit of bytes allowed to be
		queued on this network device transmit queue. See
		include/linux/dynamic_queue_limits.h for the default value.

What:		/sys/class/<iface>/queues/tx-<queue>/byte_queue_limits/limit_min
Date:		November 2011
KernelVersion:	3.3
Contact:	<EMAIL>
Description:
		Indicates the absolute minimum limit of bytes allowed to be
		queued on this network device transmit queue. Default value is
		0.
