What:		Raise a uevent when a USB Host Controller has died
Date:		2019-04-17
KernelVersion:	5.2
Contact:	<EMAIL>
Description:	When the USB Host Controller has entered a state where it is no
		longer functional a uevent will be raised. The uevent will
		contain ACTION=offline and ERROR=DEAD.

		Here is an example taken using udevadm monitor -p:

		KERNEL[130.428945] offline  /devices/pci0000:00/0000:00:10.0/usb2 (usb)
		ACTION=offline
		BUSNUM=002
		DEVNAME=/dev/bus/usb/002/001
		DEVNUM=001
		DEVPATH=/devices/pci0000:00/0000:00:10.0/usb2
		DEVTYPE=usb_device
		DRIVER=usb
		ERROR=DEAD
		MAJOR=189
		MINOR=128
		PRODUCT=1d6b/2/414
		SEQNUM=2168
		SUBSYSTEM=usb
		TYPE=9/0/1

Users:		<EMAIL>
