What:		/sys/class/backlight/<backlight>/scale
Date:		July 2019
KernelVersion:	5.4
Contact:	<PERSON> <<EMAIL>>
Description:
		Description of the scale of the brightness curve.

		The human eye senses brightness approximately logarithmically,
		hence linear changes in brightness are perceived as being
		non-linear. To achieve a linear perception of brightness changes
		controls like sliders need to apply a logarithmic mapping for
		backlights with a linear brightness curve.

		Possible values of the attribute are:

		unknown
		  The scale of the brightness curve is unknown.

		linear
		  The brightness changes linearly with each step. Brightness
		  controls should apply a logarithmic mapping for a linear
		  perception.

		non-linear
		  The brightness changes non-linearly with each step. Brightness
		  controls should use a linear mapping for a linear perception.
