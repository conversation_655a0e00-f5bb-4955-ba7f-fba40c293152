What:		/sys/class/stm_source/<stm_source>/stm_source_link
Date:		June 2015
KernelVersion:	4.3
Contact:	<PERSON> <<EMAIL>>
Description:
		stm_source device linkage to stm device, where its tracing data
		is directed. Reads return an existing connection or "<none>" if
		this stm_source is not connected to any stm device yet.
		Write an existing (registered) stm device's name here to
		connect that device. If a device is already connected to this
		stm_source, it will first be disconnected.
