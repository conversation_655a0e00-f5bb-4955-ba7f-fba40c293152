What:		/sys/kernel/uids/<uid>/cpu_shares
Date:		December 2007
Contact:	<PERSON><PERSON><PERSON> <<EMAIL>>
		<PERSON><PERSON><PERSON> <<EMAIL>>
Description:
		The /sys/kernel/uids/<uid>/cpu_shares tunable is used
		to set the cpu bandwidth a user is allowed. This is a
		propotional value. What that means is that if there
		are two users logged in, each with an equal number of
		shares, then they will get equal CPU bandwidth. Another
		example would be, if User A has shares = 1024 and user
		B has shares = 2048, User B will get twice the CPU
		bandwidth user A will. For more details refer
		Documentation/scheduler/sched-design-CFS.rst
