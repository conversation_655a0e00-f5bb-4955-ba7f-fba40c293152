What:		/sys/devices/platform/eeepc/disp
Date:		May 2008
KernelVersion:	2.6.26
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		This file allows display switching.
		- 1 = LCD
		- 2 = CRT
		- 3 = LCD+CRT
		If you run X11, you should use xrandr instead.

What:		/sys/devices/platform/eeepc/camera
Date:		May 2008
KernelVersion:	2.6.26
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the camera. 1 means on, 0 means off.

What:		/sys/devices/platform/eeepc/cardr
Date:		May 2008
KernelVersion:	2.6.26
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Control the card reader. 1 means on, 0 means off.

What:		/sys/devices/platform/eeepc/cpufv
Date:		Jun 2009
KernelVersion:	2.6.31
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		Change CPU clock configuration.
		On the Eee PC 1000H there are three available clock configuration:
		    * 0 -> Super Performance Mode
		    * 1 -> High Performance Mode
		    * 2 -> Power Saving Mode
		On Eee PC 701 there is only 2 available clock configurations.
		Available configuration are listed in available_cpufv file.
		Reading this file will show the raw hexadecimal value which
		is defined as follow:
		| 8 bit | 8 bit |
		    |       `---- Current mode
		    `------------ Availables modes
		For example, 0x301 means: mode 1 selected, 3 available modes.

What:		/sys/devices/platform/eeepc/available_cpufv
Date:		Jun 2009
KernelVersion:	2.6.31
Contact:	"Corentin Chary" <<EMAIL>>
Description:
		List available cpufv modes.
