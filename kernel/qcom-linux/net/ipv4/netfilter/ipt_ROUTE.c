/*
 * This implements the ROUTE target, which enables you to setup unusual
 * routes not supported by the standard kernel routing table.
 *
 * Copyright (C) 2002 <PERSON><PERSON> <<EMAIL>>
 *
 * v 1.11 2004/11/23
 *
 * This software is distributed under GNU GPL v2, 1991
 */

#include <linux/module.h>
#include <linux/skbuff.h>
#include <linux/ip.h>
#include <linux/netdevice.h>
#include <linux/route.h>
#include <linux/version.h>
#include <linux/if_arp.h>
#include <net/ip.h>
#include <net/arp.h>
#include <net/route.h>
#include <net/icmp.h>
#include <net/checksum.h>

#include <linux/netfilter/x_tables.h>
#include <linux/netfilter_ipv4/ip_tables.h>
#include <linux/netfilter_ipv4/ipt_ROUTE.h>

#include <net/netfilter/ipv4/nf_conntrack_ipv4.h>
#include <net/netfilter/nf_conntrack.h>

#if 1
#define DEBUGP printk
#else
#define DEBUGP(format, args...)
#endif

#define NIPQUAD(addr) \
	((unsigned char *)&addr)[0], \
	((unsigned char *)&addr)[1], \
	((unsigned char *)&addr)[2], \
	((unsigned char *)&addr)[3]

MODULE_LICENSE("GPL");
MODULE_AUTHOR("Cedric de Launois <<EMAIL>>");
MODULE_DESCRIPTION("iptables ROUTE target module");

/* Cradlepoint:
 * FIXME: This has gotten horribly ugly, with us essentially duplicating ip_output.c to
 * allow bypassing most of the networking stack.  We could probably vastly simplify this
 * by adding a path in ip_output itself for us to use.
 * /Cradlepoint */

static struct net *pick_net(struct sk_buff *skb)
{
#ifdef CONFIG_NET_NS
	const struct dst_entry *dst;

	if (skb->dev != NULL)
		return dev_net(skb->dev);
	dst = skb_dst(skb);
	if (dst != NULL && dst->dev != NULL)
		return dev_net(dst->dev);
#endif
	return &init_net;
}

/* Stolen from ip_exceeds_mtu */
static bool ip_direct_exceeds_mtu(const struct sk_buff *skb, unsigned int mtu)
{
	if (skb->len <= mtu)
		return false;

	if (unlikely((ip_hdr(skb)->frag_off & htons(IP_DF)) == 0))
		return false;

	/* original fragment exceeds mtu and DF is set */
	if (unlikely(IPCB(skb)->frag_max_size > mtu))
		return true;

	if (skb->ignore_df)
		return false;

	if (skb_is_gso(skb) && skb_gso_validate_network_len(skb, mtu))
		return false;

	return true;
}

/* Try to route the packet according to the routing keys specified in
 * route_info. Keys are :
 *  - ifindex : 
 *      0 if no oif preferred, 
 *      otherwise set to the index of the desired oif
 *  - route_info->gw :
 *      0 if no gateway specified,
 *      otherwise set to the next host to which the pkt must be routed
 * If success, skb->dev is the output device to which the packet must 
 * be sent and skb->dst is not NULL
 *
 * NOTE: This will decrement packet TTL unless IPT_TTL_PASSTHROUGH is set in
 * the ipt_route_target_info.flags.
 *
 *
 * RETURN: -1 if an error occured
 *          1 if the packet was succesfully routed to the 
 *            destination desired
 *          0 if the kernel routing table could not route the packet
 *            according to the keys specified
 */
static int route(struct sk_buff *skb,
		 unsigned int ifindex,
		 const struct ipt_route_target_info *route_info)
{
	struct iphdr *iph = ip_hdr(skb);
	struct net *net = pick_net(skb);
	struct flowi4 fl4 = {
		.daddr = iph->daddr,
		.flowi4_iif = skb->skb_iif ? : LOOPBACK_IFINDEX,
		.flowi4_oif = ifindex,
		.flowi4_tos = RT_TOS(iph->tos),
		.flowi4_scope = RT_SCOPE_UNIVERSE,
		.flowi4_flags = FLOWI_FLAG_KNOWN_NH,
	};
	struct rtable *rt;
	u32 mtu;

	/* The destination address may be overloaded by the target */
	if (route_info->gw)
		fl4.daddr = route_info->gw;

	/* Trying to route the packet using the standard routing table. */
	rt = ip_route_output_key_hash(net, &fl4, skb , true);
	if (IS_ERR(rt)) {
		if (net_ratelimit())
			DEBUGP("ipt_ROUTE: couldn't route pkt (err: %ld)\n", PTR_ERR(rt));
		return -1;
	}

	/* Drop old route. */
	skb_dst_drop(skb);

	/* Check for DF and fragmentation and handle any ICMP responses needed
	 * before we start messing with the skb. Stolen from ip_forward. */
	IPCB(skb)->flags |= IPSKB_FORWARDED;
	mtu = ip_dst_mtu_maybe_forward(&rt->dst, true);
	if (ip_direct_exceeds_mtu(skb, mtu)) {
		/* Need to create an input route so the ICMP response goes the right place */
		if (ip_route_input(skb, iph->daddr, iph->saddr, iph->tos, skb->dev) != 0) {
			if (net_ratelimit())
				DEBUGP("ipt_ROUTE: couldn't input route ICMP response\n");
		} else {
			IP_INC_STATS(dev_net(rt->dst.dev), IPSTATS_MIB_FRAGFAILS);
			icmp_send(skb, ICMP_DEST_UNREACH, ICMP_FRAG_NEEDED,
				  htonl(mtu));
		}
		ip_rt_put(rt);
		return -1;
	}

	/* Success if no oif specified or if the oif correspond to the 
	 * one desired, we know we will forward */
	if (!ifindex || rt->dst.dev->ifindex == ifindex) {
		skb_dst_set(skb, &rt->dst);
		skb->dev = rt->dst.dev;
		skb->protocol = htons(ETH_P_IP);

		/* We are about to mangle packet. Copy it! */
		if (skb_cow(skb, LL_RESERVED_SPACE(rt->dst.dev)+rt->dst.header_len)) {
			if (net_ratelimit())
				DEBUGP("ipt_ROUTE: couldn't copy SKB for mangling\n");
			return -1;
		}

		/* Decrease ttl after skb cow done, but only if we don't want pass TTL
		 * through as well. */
		if (!(route_info->flags & IPT_ROUTE_TTL_PASSTHROUGH))
			ip_decrease_ttl(iph);

		return 1;
	}

	/* The interface selected by the routing table is not the one
	 * specified by the user. This may happen because the dst address
	 * is one of our own addresses.
	 */
	if (net_ratelimit())
		DEBUGP("ipt_ROUTE: failed to route as desired gw=%u.%u.%u.%u oif=%i (got oif=%i)\n",
			NIPQUAD(route_info->gw), ifindex, rt->dst.dev->ifindex);

	ip_rt_put(rt);
	return 0;
}


/* Stolen from ip_finish_output2
 * PRE : skb->dev is set to the device we are leaving by
 *       skb->dst is not NULL
 * POST: the packet is sent with the link layer header pushed
 *       the packet is destroyed
 */
static int ip_direct_send2(struct net *net, struct sock *sk, struct sk_buff *skb, u32 nexthop)
{
	struct dst_entry *dst = skb_dst(skb);
	struct rtable *rt = (struct rtable *)dst;
	struct net_device *dev = dst->dev;
	unsigned int hh_len = LL_RESERVED_SPACE(dev);
	struct neighbour *neigh;
	bool is_v6gw = false;

	if (rt->rt_type == RTN_MULTICAST) {
		IP_UPD_PO_STATS(net, IPSTATS_MIB_OUTMCAST, skb->len);
	} else if (rt->rt_type == RTN_BROADCAST)
		IP_UPD_PO_STATS(net, IPSTATS_MIB_OUTBCAST, skb->len);

	/* Be paranoid, rather than too clever. */
	if (unlikely(skb_headroom(skb) < hh_len && dev->header_ops)) {
		struct sk_buff *skb2;

		skb2 = skb_realloc_headroom(skb, LL_RESERVED_SPACE(dev));
		if (!skb2) {
			kfree_skb(skb);
			return -ENOMEM;
		}
		if (skb->sk)
			skb_set_owner_w(skb2, skb->sk);
		consume_skb(skb);
		skb = skb2;
	}

	rcu_read_lock_bh();
	neigh = ip_neigh_for_gw(rt, skb, &is_v6gw);
	if (!IS_ERR(neigh)) {
		int res;
		sock_confirm_neigh(skb, neigh);
		/* if crossing protocols, can not use the cached header */
		res = neigh_output(neigh, skb, is_v6gw);
		rcu_read_unlock_bh();
		return res;
	}
	rcu_read_unlock_bh();

	net_dbg_ratelimited("ipt_ROUTE (%s): No header cache and no neighbour!\n",
			    __func__);
	kfree_skb(skb);
	return -EINVAL;
}

/* Stolen from ip_fragment */
static int ip_direct_fragment(struct net *net, struct sock *sk, struct sk_buff *skb,
		       unsigned int mtu, u32 nexthop,
		       int (*output)(struct net *, struct sock *, struct sk_buff *, u32 nexthop))
{
	struct iphdr *iph = ip_hdr(skb);

	if ((iph->frag_off & htons(IP_DF)) == 0)
		return ip_do_fragment2(net, sk, skb, nexthop, NULL, output);

	if (unlikely(!skb->ignore_df ||
		     (IPCB(skb)->frag_max_size &&
		      IPCB(skb)->frag_max_size > mtu))) {
		IP_INC_STATS(net, IPSTATS_MIB_FRAGFAILS);
		icmp_send(skb, ICMP_DEST_UNREACH, ICMP_FRAG_NEEDED,
			  htonl(mtu));
		kfree_skb(skb);
		return -EMSGSIZE;
	}

	return ip_do_fragment2(net, sk, skb, nexthop, NULL, output);
}

/* Stolen from ip_finish_output_gso */
static int ip_direct_output_gso(struct net *net, struct sock *sk,
				struct sk_buff *skb, unsigned int mtu,
				u32 nexthop)
{
	netdev_features_t features;
	struct sk_buff *segs;
	int ret = 0;

	/* common case: locally created skb or seglen is <= mtu */
	if (((IPCB(skb)->flags & IPSKB_FORWARDED) == 0) ||
			(skb_gso_validate_network_len(skb, mtu)))
		return ip_direct_send2(net, sk, skb, nexthop);

	/* Slowpath -  GSO segment length is exceeding the dst MTU.
	 *
	 * This can happen in two cases:
	 * 1) TCP GRO packet, DF bit not set
	 * 2) skb arrived via virtio-net, we thus get TSO/GSO skbs directly
	 * from host network stack.
	 */
	features = netif_skb_features(skb);
	BUILD_BUG_ON(sizeof(*IPCB(skb)) > SKB_SGO_CB_OFFSET);
	segs = skb_gso_segment(skb, features & ~NETIF_F_GSO_MASK);
	if (IS_ERR_OR_NULL(segs)) {
		kfree_skb(skb);
		return -ENOMEM;
	}

	consume_skb(skb);

	do {
		struct sk_buff *nskb = segs->next;
		int err;

		segs->next = NULL;
		err = ip_direct_fragment(net, sk, segs, mtu, nexthop, ip_direct_send2);

		if (err && ret == 0)
			ret = err;
		segs = nskb;
	} while (segs);

	return ret;
}

/* Stolen from ip_finish_output */
static int ip_direct_send(struct net *net, struct sock *sk, struct sk_buff *skb, u32 nexthop)
{
	unsigned int mtu;

	mtu = ip_skb_dst_mtu(sk, skb);
	if (skb_is_gso(skb))
		return ip_direct_output_gso(net, sk, skb, mtu, nexthop);

	if (skb->len > mtu || (IPCB(skb)->flags & IPSKB_FRAG_PMTU))
		return ip_direct_fragment(net, sk, skb, mtu, nexthop, ip_direct_send2);

	return ip_direct_send2(net, sk, skb, nexthop);
}


/* PRE : skb->dev is set to the device we are leaving by
 * POST: - the packet is directly sent to the skb->dev device, without 
 *         pushing the link layer header.
 *       - the packet is destroyed
 */
static inline int dev_direct_send(struct sk_buff *skb)
{
	return dev_queue_xmit(skb);
}


static unsigned int route_oif(const struct ipt_route_target_info *route_info,
			      struct sk_buff *skb) 
{
	unsigned int ifindex = 0;
	struct net_device *dev_out = NULL;

	/* The user set the interface name to use.
	 * Getting the current interface index.
	 */
	if ((dev_out = dev_get_by_name(&init_net, route_info->oif))) {
		ifindex = dev_out->ifindex;
	} else {
		/* Unknown interface name : packet dropped */
		if (net_ratelimit())
			DEBUGP(KERN_DEBUG "ipt_ROUTE: oif interface %s not found\n", route_info->oif);
		return NF_DROP;
	}

	/* Trying the standard way of routing packets */
	switch (route(skb, ifindex, route_info)) {
	case 1:
		dev_put(dev_out);
		if (route_info->flags & IPT_ROUTE_CONTINUE)
			return XT_CONTINUE;

		ip_direct_send(pick_net(skb), NULL, skb, route_info->gw);
		return NF_STOLEN;

	case 0:
		/* Failed to send to oif. Trying the hard way */
		if (route_info->flags & IPT_ROUTE_CONTINUE) {
			dev_put(dev_out);
			return NF_DROP;
		}

		if (net_ratelimit()) 
			DEBUGP("ipt_ROUTE: forcing the use of %i\n",
			       ifindex);

		/* We have to force the use of an interface.
		 * This interface must be a tunnel interface since
		 * otherwise we can't guess the hw address for
		 * the packet. For a tunnel interface, no hw address
		 * is needed.
		 */
		if ((dev_out->type != ARPHRD_TUNNEL)
		    && (dev_out->type != ARPHRD_IPGRE)) {
			if (net_ratelimit()) 
				DEBUGP("ipt_ROUTE: can't guess the hw addr !\n");
			dev_put(dev_out);
			return NF_DROP;
		}
	
		/* Send the packet. This will also free skb
		 * Do not go through the POST_ROUTING hook because 
		 * skb->dst is not set and because it will probably
		 * get confused by the destination IP address.
		 */
		skb->dev = dev_out;
		dev_direct_send(skb);
		dev_put(dev_out);
		return NF_STOLEN;
		
	default:
		/* Unexpected error */
		dev_put(dev_out);
		return NF_DROP;
	}
}


static unsigned int route_iif(const struct ipt_route_target_info *route_info,
			      struct sk_buff *skb) 
{
	struct net_device *dev_in = NULL;

	/* Getting the current interface index. */
	if (!(dev_in = dev_get_by_name(&init_net, route_info->iif))) {
		if (net_ratelimit()) 
			DEBUGP("ipt_ROUTE: iif interface %s not found\n", route_info->iif);
		return NF_DROP;
	}

	skb_dst_drop(skb);
	skb->dev = dev_in;

	netif_rx(skb);
	dev_put(dev_in);
	return NF_STOLEN;
}


static unsigned int route_gw(const struct ipt_route_target_info *route_info,
			     struct sk_buff *skb) 
{
	if (route(skb, 0, route_info) != 1)
		return NF_DROP;

	if (route_info->flags & IPT_ROUTE_CONTINUE)
		return XT_CONTINUE;

	ip_direct_send(pick_net(skb), NULL, skb, route_info->gw);
	return NF_STOLEN;
}


static unsigned int ipt_route_target(struct sk_buff *skb,
				     const struct xt_action_param *par)
{
	const struct ipt_route_target_info *route_info = par->targinfo;
	unsigned int res;
	enum ip_conntrack_info ctinfo;
	struct nf_conn *ct;

	/* If we are at PREROUTING or INPUT hook
	 * the TTL isn't decreased by the IP stack
	 */
	if (xt_hooknum(par) == NF_INET_PRE_ROUTING ||
	    xt_hooknum(par)  == NF_INET_LOCAL_IN) {

		struct iphdr *iph = ip_hdr(skb);

		if (iph->ttl <= 1) {
			struct net *net = pick_net(skb);
			struct rtable *rt;
			struct flowi4 fl4;

			memset(&fl4, 0, sizeof(fl4));
			fl4.flowi4_tos = RT_TOS(iph->tos);
			fl4.flowi4_scope = ((iph->tos & RTO_ONLINK) ?
						RT_SCOPE_LINK : RT_SCOPE_UNIVERSE);
			fl4.flowi4_flags = FLOWI_FLAG_KNOWN_NH;
			fl4.daddr = iph->daddr;
			fl4.saddr = iph->saddr;

			rt = ip_route_output_key(net, &fl4);
			if (IS_ERR(rt)) {
				return NF_DROP;
			}

			if (skb->dev == rt->dst.dev) {
				/* Drop old route. */
				skb_dst_drop(skb);
				skb_dst_set(skb, &rt->dst);

				/* this will traverse normal stack, and 
				 * thus call conntrack on the icmp packet */
				icmp_send(skb, ICMP_TIME_EXCEEDED, 
						ICMP_EXC_TTL, 0);
			} else
				ip_rt_put(rt);

			return NF_DROP;
		}
	}

	if ((route_info->flags & IPT_ROUTE_TEE)) {
		/*
		 * Copy the skb, and route the copy. Will later return
		 * XT_CONTINUE for the original skb, which should continue
		 * on its way as if nothing happened. The copy should be
		 * independantly delivered to the ROUTE --gw.
		 */
		skb = skb_copy(skb, GFP_ATOMIC);
		if (!skb) {
			if (net_ratelimit()) 
				DEBUGP(KERN_DEBUG "ipt_ROUTE: copy failed!\n");
			return XT_CONTINUE;
		}
	}

	if (!(route_info->flags & IPT_ROUTE_CONTINUE)) {
		ct = nf_ct_get(skb, &ctinfo);
		if (ct) {
			nf_conntrack_put(&ct->ct_general);
			nf_ct_set(skb, NULL, IP_CT_UNTRACKED);
		}
	}

	if (route_info->oif[0] != '\0') {
		res = route_oif(route_info, skb);
	} else if (route_info->iif[0] != '\0') {
		res = route_iif(route_info, skb);
	} else if (route_info->gw) {
		res = route_gw(route_info, skb);
	} else {
		if (net_ratelimit()) 
			DEBUGP(KERN_DEBUG "ipt_ROUTE: no parameter !\n");
		res = XT_CONTINUE;
	}

	if ((route_info->flags & IPT_ROUTE_TEE))
		res = XT_CONTINUE;

	return res;
}

static struct xt_target xt_route_reg = { 
	.name = "ROUTE",
	.target = ipt_route_target,
	.family = NFPROTO_IPV4,
	.targetsize = sizeof(struct ipt_route_target_info),
	.table = "mangle",
	.hooks = 1 << NF_INET_PRE_ROUTING | 1 << NF_INET_LOCAL_IN |
		 1 << NF_INET_FORWARD | 1 << NF_INET_LOCAL_OUT |
		 1 << NF_INET_POST_ROUTING,
	.me = THIS_MODULE,
};

static int __init init(void)
{
	return xt_register_target(&xt_route_reg);
}


static void __exit fini(void)
{
	xt_unregister_target(&xt_route_reg);
}

module_init(init);
module_exit(fini);
